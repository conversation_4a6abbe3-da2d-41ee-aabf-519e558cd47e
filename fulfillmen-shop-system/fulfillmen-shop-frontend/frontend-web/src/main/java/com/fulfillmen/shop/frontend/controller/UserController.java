/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.frontend.controller;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.stp.StpUtil;
import com.fulfillmen.shop.common.annotation.RateLimit;
import com.fulfillmen.shop.common.context.UserContextHolder;
import com.fulfillmen.shop.common.enums.RateLimitAlgorithm;
import com.fulfillmen.shop.common.enums.RateLimitKeyType;
import com.fulfillmen.shop.common.enums.RateLimitStorageType;
import com.fulfillmen.shop.domain.req.UserUpdateRep;
import com.fulfillmen.shop.domain.res.UserInfoRes;
import com.fulfillmen.shop.frontend.service.user.IUserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 用户信息相关接口
 *
 * <AUTHOR>
 * @date 2025/7/8
 * @description: todo
 * @since 1.0.0
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/api/user")
@RequiredArgsConstructor
@Tag(name = "用户信息", description = "用户信息相关接口")
public class UserController {

    private final IUserService userService;

    @GetMapping("/info")
    @Operation(summary = "获取用户信息", description = "获取当前登录用户的详细信息")
    @SaCheckLogin
    public UserInfoRes getUserInfo() {
        return userService.getUserInfo(UserContextHolder.getUserId());
    }

    @PutMapping("/password")
    @Operation(summary = "修改密码", description = "修改当前登录用户的密码")
    @SaCheckLogin
    @RateLimit(
        keyType = RateLimitKeyType.USER_ID, storageType = RateLimitStorageType.REDIS, algorithm = RateLimitAlgorithm.FIXED_WINDOW, rate = 3, window = 2592000, // 30 days
        keyPrefix = "update_password_limit", description = "用户修改密码频率限制"
    )
    public void updatePassword(@Valid @RequestBody UserUpdateRep.UserUpdatePasswordReq request) {
        log.info("用户[{}]请求修改密码", UserContextHolder.getUserId());
        userService.updatePassword(UserContextHolder.getUserId(), request);
    }

    @GetMapping("/send-resetCode")
    @RateLimit(keyType = RateLimitKeyType.IP, storageType = RateLimitStorageType.REDIS, algorithm = RateLimitAlgorithm.SLIDING_WINDOW, rate = 30, window = 3600, keyPrefix = "email_captcha_rate_limit", description = "邮箱验证码发送限流")
    @Operation(summary = "重置发送验证码", description = "重置触发发送邮箱验证码")
    @SaCheckLogin
    public ResponseEntity<Void> sendResetPasswordEmailCaptcha() {
        // 1. 使用 Sa-Token 的标准方式获取用户ID，用于日志记录
        log.info("用户[{}]触发发送邮箱验证码", StpUtil.getLoginIdAsString());

        // 2. 调用无参数的服务方法
        userService.sendResetPasswordEmailCaptcha();

        // 3. 返回标准的成功响应
        return ResponseEntity.ok().build();
    }

    @PutMapping("/reset-password")
    @Operation(summary = "重置密码", description = "重置用户的密码")
    @SaCheckLogin
    @RateLimit(
        keyType = RateLimitKeyType.USER_ID, storageType = RateLimitStorageType.REDIS, algorithm = RateLimitAlgorithm.FIXED_WINDOW, rate = 3, window = 2592000, keyPrefix = "reset_password_with_email_limit", description = "通过邮箱重置密码频率限制"
    )
    public void resetPassword(@Valid @RequestBody UserUpdateRep.ResetPasswordReq request) {
        log.info("用户[{}]请求重置密码", UserContextHolder.getUserId());
        userService.resetPasswordWithEmail(request);
    }

    @PostMapping("/send-emailCode")
    @RateLimit(keyType = RateLimitKeyType.IP, storageType = RateLimitStorageType.REDIS, algorithm = RateLimitAlgorithm.SLIDING_WINDOW, rate = 30, window = 3600, keyPrefix = "email_captcha_rate_limit", description = "邮箱验证码发送限流")
    @Operation(summary = "发送邮箱验证码", description = "发送邮箱验证码到新邮箱")
    @SaCheckLogin
    public ResponseEntity<String> sendUpdateEmailCaptcha(@Valid @RequestBody UserUpdateRep.SendUpdateEmailCaptchaReq request) {
        log.info("用户[{}]触发发送邮箱验证码, request: {}", StpUtil.getLoginIdAsString(), request);
        String captchaId = userService.sendUpdateEmailCaptcha(request);
        return ResponseEntity.ok(captchaId);
    }

    @PutMapping("/email")
    @Operation(summary = "修改邮箱", description = "修改当前登录用户的邮箱")
    @SaCheckLogin
    @RateLimit(
        keyType = RateLimitKeyType.USER_ID, storageType = RateLimitStorageType.REDIS, algorithm = RateLimitAlgorithm.FIXED_WINDOW, rate = 3, window = 2592000, keyPrefix = "update_email_limit", description = "用户修改邮箱频率限制"
    )
    public ResponseEntity<Void> updateEmail(@Valid @RequestBody UserUpdateRep.UserUpdateEmailReq request) {
        log.info("用户[{}]请求修改邮箱, request body: {}", UserContextHolder.getUserId(), request);
        userService.updateEmail(request);
        return ResponseEntity.ok().build();
    }

    @PutMapping("/info")
    @Operation(summary = "修改用户信息", description = "修改当前用户信息")
    @SaCheckLogin
    public void updateInfo(@Valid @RequestBody UserUpdateRep.UserUpdateInfoReq request) {
        log.info("用户[{}]请求修改信息", UserContextHolder.getUserId());
        userService.updateInfo(UserContextHolder.getUserId(), request);
    }

    @PutMapping("/default-warehouse")
    @Operation(summary = "设置默认仓库", description = "设置当前用户默认仓库")
    @SaCheckLogin
    public void setDefaultWarehouse(@Valid @RequestBody UserUpdateRep.setDefaultWarehouseReq request) {
        log.info("用户[{}]请求设置默认仓库", UserContextHolder.getUserId());
        userService.setDefaultWarehouse(request.getWarehouseId());
    }
}
