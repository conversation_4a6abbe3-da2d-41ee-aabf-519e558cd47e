/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.frontend.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fulfillmen.shop.dao.mapper.TzUserMapper;
import com.fulfillmen.shop.domain.entity.TzUser;
import com.fulfillmen.shop.domain.req.UserAuthReq.UserRegistryDTO;
import com.fulfillmen.shop.frontend.service.impl.user.UserServiceImpl;
import com.fulfillmen.shop.manager.core.common.ICaptchaManager;

import jakarta.servlet.http.HttpServletRequest;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * 用户注册服务单元测试
 *
 * <AUTHOR>
 * @date 2025/4/24
 */
@ExtendWith(MockitoExtension.class)
class UserRegistryServiceImplTest {

    @Mock
    private TzUserMapper tzUserMapper;

    @Mock
    private ICaptchaManager captchaManager;

    @Mock
    private HttpServletRequest request;

    @InjectMocks
    private UserServiceImpl userRegistryService;

    @Test
    @DisplayName("测试用户注册")
    void testUserRegistration() {
        // 准备测试数据
        UserRegistryDTO userDto = UserRegistryDTO.builder()
            .username("testuser")
            .email("<EMAIL>")
            .password("Test1234")
            .mobile("+8613800138000")
            .build();

        // Mock请求IP
        Mockito.when(request.getRemoteAddr()).thenReturn("127.0.0.1");

        // Mock数据库查询，返回null表示用户不存在
        Mockito.when(tzUserMapper.selectOne(Mockito.any(LambdaQueryWrapper.class))).thenReturn(null);

        // Mock数据库插入操作
        Mockito.when(tzUserMapper.insert(Mockito.any(TzUser.class))).thenReturn(1);

        // 执行注册
        userRegistryService.registry(userDto, request);

        // 验证交互
        Mockito.verify(tzUserMapper).selectOne(Mockito.any(LambdaQueryWrapper.class));
        Mockito.verify(tzUserMapper).insert(Mockito.any(TzUser.class));
    }

    @Test
    @DisplayName("测试用户注册 - 打印流程")
    void testRegistryProcess() {
        System.out.println("=== 用户注册流程测试 ===");
        System.out.println("1. 接收用户注册请求");
        System.out.println("2. 校验用户名和邮箱是否已存在");
        System.out.println("3. 生成密码盐值并加密密码");
        System.out.println("4. 将用户信息保存到数据库");
        System.out.println("5. 注册成功，返回结果");
        System.out.println("=== 测试结束 ===");
    }
}
