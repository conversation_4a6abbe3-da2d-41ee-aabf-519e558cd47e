/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.frontend.service.impl;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fulfillmen.shop.dao.mapper.TzUserMapper;
import com.fulfillmen.shop.frontend.config.TestApplicationConfig;
import com.fulfillmen.shop.frontend.service.user.IUserService;
import com.fulfillmen.shop.domain.entity.TzUser;
import com.fulfillmen.shop.domain.req.UserAuthReq.UserRegistryDTO;
import java.util.UUID;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

/**
 * 用户注册服务集成测试 此测试会连接实际数据库进行测试
 *
 * <AUTHOR>
 * @date 2025/4/24
 */
@SpringBootTest(classes = TestApplicationConfig.class)
@ActiveProfiles("test")
class UserRegistryServiceIntegrationTest {

    @Autowired
    private IUserService userRegistryService;

    @Autowired
    private TzUserMapper tzUserMapper;

    // 测试数据
    private String testUsername;
    private String testEmail;
    private String testMobile;
    private MockHttpServletRequest request;

    @BeforeEach
    void setUp() {
        // 初始化HTTP请求
        request = new MockHttpServletRequest();
        request.setRemoteAddr("127.0.0.1");

        // 生成随机测试数据，避免测试间冲突
        String random = UUID.randomUUID().toString().substring(0, 8);
        testUsername = "test_user_" + random;
        testEmail = "test_" + random + "@example.com";
        testMobile = "+8613800138" + random.substring(0, 3);
    }

    @AfterEach
    void tearDown() {
        // 清理测试数据
        try {
            QueryWrapper<TzUser> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("username", testUsername);
            tzUserMapper.delete(queryWrapper);
        } catch (Exception e) {
            System.err.println("清理测试数据失败: " + e.getMessage());
        }
    }

    @Test
    @Transactional // 使用事务注解确保测试后数据自动回滚
    @DisplayName("集成测试 - 用户注册成功")
    void testRegistrySuccess() {
        // 准备测试数据
        UserRegistryDTO userRegistryDTO = UserRegistryDTO.builder()
            .username(testUsername)
            .email(testEmail)
            .password("Test1234") // 密码需要符合规则：包含大小写字母和数字
            .mobile(testMobile) // mobile
            .captchaCode(null) // captcha
            .captchaId("127.0.0.1") // registerIp
            .country("China") // country
            .state("Shanghai") // state
            .city("Shanghai") // city
            .zipCode("200000") // zipCode
            .build();

        // 执行注册
        userRegistryService.registry(userRegistryDTO, request);

        // 验证注册结果 - 使用字符串字段名
        QueryWrapper<TzUser> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("username", testUsername);
        TzUser user = tzUserMapper.selectOne(queryWrapper);

        // 验证用户是否创建成功
        assertNotNull(user, "注册用户不应为空");

        // 简单打印测试结果，不使用getter方法
        System.out.println("集成测试 - 用户注册成功！");
        System.out.println("用户名: " + testUsername);
        System.out.println("邮箱: " + testEmail);
    }

    @Test
    @Transactional
    @DisplayName("集成测试 - 重复用户名注册失败")
    void testRegistryDuplicateUsername() {
        // 准备第一个用户数据并注册
        UserRegistryDTO firstUser = new UserRegistryDTO(testUsername, testEmail, "Test1234", testMobile, "China", "Shanghai", "Shanghai", "200000", null, "127.0.0.1");
        userRegistryService.registry(firstUser, request);

        // 准备第二个用户数据，使用相同用户名
        UserRegistryDTO secondUser = new UserRegistryDTO(testUsername, // 相同用户名
            "another_" + testEmail, "Test1234", "+8613900139000", "China", "Beijing", "Beijing", "100000", null, "127.0.0.1");

        // 尝试注册相同用户名，应该抛出异常
        Exception exception = assertThrows(Exception.class, () -> {
            userRegistryService.registry(secondUser, request);
        });

        // 验证异常消息中包含用户名已存在
        assertTrue(exception.getMessage().contains("username is exist"), "异常消息应包含'username is exist'");

        System.out.println("集成测试 - 重复用户名注册失败！");
        System.out.println("预期的异常: " + exception.getMessage());
    }

    @Test
    @Transactional
    @DisplayName("集成测试 - 重复邮箱注册失败")
    void testRegistryDuplicateEmail() {
        // 准备第一个用户数据并注册
        UserRegistryDTO firstUser = new UserRegistryDTO(testUsername, testEmail, "Test1234", testMobile, "China", "Shanghai", "Shanghai", "200000", null, "127.0.0.1");
        userRegistryService.registry(firstUser, request);

        // 准备第二个用户数据，使用相同邮箱
        UserRegistryDTO secondUser = new UserRegistryDTO("another_" + testUsername, testEmail, // 相同邮箱
            "Test1234", "+8613900139000", "China", "Beijing", "Beijing", "100000", null, "127.0.0.1");

        // 尝试注册相同邮箱，应该抛出异常
        Exception exception = assertThrows(Exception.class, () -> {
            userRegistryService.registry(secondUser, request);
        });

        // 验证异常消息中包含邮箱已存在
        assertTrue(exception.getMessage().contains("email is exist"), "异常消息应包含'email is exist'");

        System.out.println("集成测试 - 重复邮箱注册失败！");
        System.out.println("预期的异常: " + exception.getMessage());
    }
}
