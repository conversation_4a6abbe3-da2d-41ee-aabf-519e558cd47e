/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.frontend.convert;

import com.fulfillmen.shop.domain.entity.TzProductSpu;
import com.fulfillmen.shop.domain.entity.TzUser;
import com.fulfillmen.shop.domain.res.UserInfoRes;
import com.fulfillmen.shop.domain.vo.UserBrowseHistoryPageVO;
import com.fulfillmen.shop.domain.vo.UserFavoriteProductVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 用户注册
 *
 * <AUTHOR>
 * @date 2025/6/19
 * @description: todo
 * @since 1.0.0
 */
@Mapper
public interface FrontendUserConvert {

    public static final FrontendUserConvert INSTANCE = Mappers.getMapper(FrontendUserConvert.class);

    @Mappings({
      @Mapping(target = "userId", source = "id"),
      @Mapping(target = "username", source = "username"),
      @Mapping(target = "email", source = "email"),
      @Mapping(target = "mobile", source = "mobile"),
      @Mapping(target = "avatar", source = "avatar"),
      @Mapping(target = "gender", source = "gender"),
      @Mapping(target = "birth", source = "birth"),
      @Mapping(target = "warehouseId", source = "warehouseId"),
      @Mapping(target = "balance", source = "balance"),
    })
    UserInfoRes toDo(TzUser tzUser);

    @Mappings({
      @Mapping(target = "productId", source = "product.id"),
      @Mapping(target = "title", source = "product.title"),
      @Mapping(target = "titleEn", source = "product.titleTrans"),
      @Mapping(target = "imageUrl", source = "product.mainImage"),
      @Mapping(target = "price", source = "price"),
      @Mapping(target = "usdPrice", source = "usdPrice")
    })
    UserFavoriteProductVO toFavoriteVO(TzProductSpu product, BigDecimal price, BigDecimal usdPrice);

    @Mappings({
      @Mapping(target = "productId", source = "product.id"),
      @Mapping(target = "title", source = "product.title"),
      @Mapping(target = "titleEn", source = "product.titleTrans"),
      @Mapping(target = "imageUrl", source = "product.mainImage"),
      @Mapping(target = "lastBrowseTime", source = "lastBrowseTime"),
      @Mapping(target = "price", source = "price"),
      @Mapping(target = "usdPrice", source = "usdPrice")
    })
    UserBrowseHistoryPageVO toBrowseHistoryVO(TzProductSpu product, LocalDateTime lastBrowseTime, BigDecimal price, BigDecimal usdPrice);

}
