<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fulfillmen.shop.dao.mapper.SysAlibabaCallbackLogsMapper">
  <resultMap id="BaseResultMap" type="com.fulfillmen.shop.domain.entity.SysAlibabaCallbackLogs">
    <!--@mbg.generated-->
    <!--@Table sys_alibaba_callback_logs-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="metadata" jdbcType="VARCHAR" property="metadata" />
    <result column="order_id" jdbcType="VARCHAR" property="orderId" />
    <result column="event_type" jdbcType="VARCHAR" property="eventType" />
    <result column="received_timestamp" jdbcType="TIMESTAMP" property="receivedTimestamp" />
    <result column="process_status" jdbcType="TINYINT" property="processStatus" />
    <result column="process_failed_msg" jdbcType="LONGVARCHAR" property="processFailedMsg" />
    <result column="retry_count" jdbcType="TINYINT" property="retryCount" />
    <result column="sign" jdbcType="VARCHAR" property="sign" />
    <result column="gmt_created" jdbcType="TIMESTAMP" property="gmtCreated" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="gmt_born_virtual" jdbcType="BIGINT" property="gmtBornVirtual" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, metadata, order_id, event_type, received_timestamp, process_status, process_failed_msg,
    retry_count,sign, gmt_created, gmt_modified,gmt_born_virtual
  </sql>

  <select id="findLatestUnprocessedLogs" resultMap="BaseResultMap">
    SELECT t1.*
    FROM sys_alibaba_callback_logs t1
    INNER JOIN (
        SELECT order_id, MAX(gmt_born_virtual) as max_born_time
        FROM sys_alibaba_callback_logs
        WHERE order_id IS NOT NULL
          AND gmt_created             <![CDATA[>= DATE_SUB(NOW(), INTERVAL #{processHours} HOUR)]]>
          AND (process_status = 0 OR process_status IS NULL)
        GROUP BY order_id
    ) t2 ON t1.order_id = t2.order_id AND t1.gmt_born_virtual = t2.max_born_time
    WHERE t1.order_id IS NOT NULL
      AND t1.gmt_created             <![CDATA[>= DATE_SUB(NOW(), INTERVAL #{processHours} HOUR)]]>
      AND (t1.process_status = 0 OR t1.process_status IS NULL)
    ORDER BY t1.gmt_born_virtual DESC
    LIMIT #{limit}
  </select>

  <select id="findAllUnprocessedLogs" resultMap="BaseResultMap">
    SELECT <include refid="Base_Column_List"/>
    FROM sys_alibaba_callback_logs
    WHERE order_id IS NOT NULL
      AND gmt_created <![CDATA[>= DATE_SUB(NOW(), INTERVAL #{processHours} HOUR)]]>
      AND (process_status = 0 OR process_status IS NULL)
    ORDER BY order_id, gmt_born_virtual ASC
    LIMIT #{limit}
  </select>

  <update id="markOrderProcessedByLatestTimestamp">
    UPDATE sys_alibaba_callback_logs
    SET process_status = 1,
        gmt_modified = NOW()
    WHERE order_id = #{orderId}
      AND (process_status = 0 OR process_status IS NULL)
      AND gmt_born_virtual             <![CDATA[<= #{latestGmtBornVirtual}]]>
  </update>
</mapper>
