/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.product.service;

import com.fulfillmen.shop.domain.dto.TzProductDTO;
import com.fulfillmen.shop.manager.product.context.ProductProcessingContext;
import com.fulfillmen.shop.manager.product.context.TenantContext;
import com.fulfillmen.shop.manager.product.context.UserPricingContext;
import java.util.List;

/**
 * 产品策略服务接口
 *
 * <pre>
 * 核心职责：
 * 1. 提供产品策略处理的统一入口
 * 2. 集成责任链模式和策略模式
 * 3. 支持产品数据的完整处理流程
 * 4. 提供策略系统的监控和管理功能
 *
 * 服务能力：
 * - 产品定价策略处理
 * - 库存策略处理
 * - 图片策略处理
 * - 策略组合和链式处理
 * - 上下文构建和管理
 *
 * 使用场景：
 * - 产品同步时的策略应用
 * - 用户请求产品时的个性化处理
 * - 批量产品处理的策略应用
 * - 产品数据的实时策略调整
 * </pre>
 *
 * <AUTHOR>
 * @date 2025/08/22
 * @since 1.0.0
 */
public interface IProductStrategyService {

    /**
     * 处理产品数据（完整流程）
     *
     * @param product 原始产品数据
     * @param context 产品处理上下文
     * @return 处理后的产品数据
     */
    TzProductDTO processProduct(TzProductDTO product, ProductProcessingContext context);

    /**
     * 处理产品数据（简化接口）
     *
     * @param product           原始产品数据
     * @param userId            用户ID
     * @param tenantId          租户ID
     * @param platformProductId 平台产品ID
     * @return 处理后的产品数据
     */
    TzProductDTO processProduct(TzProductDTO product, Long userId, String tenantId, String platformProductId);

    /**
     * 批量处理产品数据（简化接口）
     *
     * @param productDTOList    原始产品数据列表
     * @param userId            用户ID
     * @param tenantId          租户ID
     * @param platformProductId 平台产品ID
     * @return 处理后的产品数据
     */
    TzProductDTO batchProcessProduct(List<TzProductDTO> productDTOList, Long userId, String tenantId, String platformProductId);

    /**
     * 只应用定价策略
     *
     * @param product     产品数据
     * @param userContext 用户定价上下文
     * @return 处理后的产品数据
     */
    TzProductDTO applyPricingStrategy(TzProductDTO product, UserPricingContext userContext);

    /**
     * 应用定价策略（包含租户上下文）
     *
     * @param product       产品数据
     * @param userContext   用户定价上下文
     * @param tenantContext 租户上下文
     * @return 处理后的产品数据
     */
    TzProductDTO applyPricingStrategy(TzProductDTO product, UserPricingContext userContext, TenantContext tenantContext);

    /**
     * 构建产品处理上下文
     *
     * @param userId            用户ID
     * @param tenantId          租户ID
     * @param platformProductId 平台产品ID
     * @param productId         内部产品ID
     * @return 产品处理上下文
     */
    ProductProcessingContext buildProcessingContext(Long userId, String tenantId, String platformProductId, Long productId);

    /**
     * 构建用户定价上下文
     *
     * @param userId 用户ID
     * @return 用户定价上下文
     */
    UserPricingContext buildUserPricingContext(Long userId);

    /**
     * 构建租户上下文
     *
     * @param tenantId 租户ID
     * @return 租户上下文
     */
    TenantContext buildTenantContext(String tenantId);

    /**
     * 获取策略系统状态信息
     *
     * @return 状态信息
     */
    String getSystemStatus();

    /**
     * 获取策略执行统计
     *
     * @return 统计信息
     */
    String getExecutionStatistics();

    /**
     * 清理策略缓存
     */
    void clearStrategyCache();

    /**
     * 重置策略统计
     */
    void resetStatistics();
}
