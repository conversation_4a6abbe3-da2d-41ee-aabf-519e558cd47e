/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.support.wms.impl;

import com.fulfillmen.shop.common.context.OrderContextDTO;
import com.fulfillmen.shop.common.context.UserContextHolder;
import com.fulfillmen.shop.common.enums.FulfillmenErrorCodeEnum;
import com.fulfillmen.shop.common.exception.BusinessExceptionI18n;
import com.fulfillmen.shop.dao.mapper.TzOrderSupplierMapper;
import com.fulfillmen.shop.dao.mapper.TzUserMapper;
import com.fulfillmen.shop.domain.entity.TzOrderItem;
import com.fulfillmen.shop.domain.entity.TzOrderPurchase;
import com.fulfillmen.shop.domain.entity.TzUser;
import com.fulfillmen.shop.manager.support.wms.IWmsManager;
import com.fulfillmen.shop.manager.support.wms.convert.WmsPurchaseOrderConvert;
import com.fulfillmen.starter.core.util.JacksonUtil;
import com.fulfillmen.support.wms.api.WmsApiClient;
import com.fulfillmen.support.wms.dto.common.WmsPageDTO;
import com.fulfillmen.support.wms.dto.enums.WmsAsnShipmentTypeEnums;
import com.fulfillmen.support.wms.dto.enums.WmsOperationTypeEnum;
import com.fulfillmen.support.wms.dto.enums.WmsOrderStatusEnum;
import com.fulfillmen.support.wms.dto.request.PurchaseOrderDetailReq;
import com.fulfillmen.support.wms.dto.request.WmsAccountChargeV2Req;
import com.fulfillmen.support.wms.dto.request.WmsCreateInboundOrderReq;
import com.fulfillmen.support.wms.dto.request.WmsCreateOrderReq;
import com.fulfillmen.support.wms.dto.request.WmsInboundInfoReq;
import com.fulfillmen.support.wms.dto.request.WmsInboundOrderUpdateReq;
import com.fulfillmen.support.wms.dto.request.WmsOrderQueryReq;
import com.fulfillmen.support.wms.dto.request.WmsPurchaseOrderDetailReq;
import com.fulfillmen.support.wms.dto.request.WmsPurchaseOrderReq;
import com.fulfillmen.support.wms.dto.response.WmsAccountChargeInfoRes;
import com.fulfillmen.support.wms.dto.response.WmsAccountInfoRes;
import com.fulfillmen.support.wms.dto.response.WmsCreateInboundOrderRes;
import com.fulfillmen.support.wms.dto.response.WmsCreatePurchaseOrderRes;
import com.fulfillmen.support.wms.dto.response.WmsInboundInfoRes;
import com.fulfillmen.support.wms.dto.response.WmsInboundUpdateRes;
import com.fulfillmen.support.wms.dto.response.WmsPurchaseOrderDetailsItemRes;
import com.fulfillmen.support.wms.dto.response.WmsPurchaseOrderDetailsRes;
import com.fulfillmen.support.wms.dto.response.WmsPurchaseOrderInfoRes;
import com.fulfillmen.support.wms.exception.WmsApiException;
import com.google.common.collect.Lists;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * WMS 管理器实现类
 *
 * <AUTHOR>
 * @date 2025/7/7
 * @description: todo
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class WmsManagerImpl implements IWmsManager {

    private final WmsApiClient wmsApiClient;
    private final TzOrderSupplierMapper tzOrderSupplierMapper;
    private final TzUserMapper tzUserMapper;
//    private final WmsPurchaseOrderConvert wmsPurchaseOrderConvert;

    @Override
    public Optional<WmsAccountInfoRes> getWmsAccountInfo(String authCode, String cusCode) {
        log.info("开始获取WMS账户信息: authCode=[{}], cusCode=[{}]", authCode, cusCode);

        try {
            // 1. 调用WMS API获取原始响应
            WmsAccountInfoRes accountInfo = wmsApiClient.getAccountInfoByAuthCode(authCode);
            return Optional.of(accountInfo);
        } catch (WmsApiException e) {
            log.error("WMS账户信息获取失败，API调用异常: authCode=[{}], error=[{}]", authCode, e.getMessage(), e);
            return Optional.empty();
        } catch (Exception e) {
            log.error("WMS账户信息获取失败，业务处理异常: authCode=[{}], error=[{}]", authCode, e.getMessage(), e);
            return Optional.empty();
        }
    }

    @Override
    public Optional<WmsAccountChargeInfoRes> deductAccountInfoByPurchase(OrderContextDTO orderContextDTO,
        String cusCode) throws WmsApiException {
        WmsAccountChargeV2Req request = WmsAccountChargeV2Req.builder()
            .orderNo(orderContextDTO.getPurchaseOrderNo())
            .cusCode(cusCode)
            .operationType(WmsOperationTypeEnum.CHARGE)
            .originalTotalPrice(orderContextDTO.getCustomerTotalAmount())
            .productAmount(orderContextDTO.getCustomerGoodsAmount())
            .serviceFee(orderContextDTO.getCustomerTotalServiceFee())
            .shippingFee(orderContextDTO.getCustomerTotalFreight())
            .remark(orderContextDTO.generatePaymentDeductionNote())
            .build();
        return Optional.ofNullable(wmsApiClient.deductAccountInfoByPurchase(request));
    }

    @Override
    public Optional<WmsAccountChargeInfoRes> refundAccountInfoByPurchase(String supplierOrderId, OrderContextDTO orderContextDTO,
        String cusCode) {
        // TODO: 2025/8/1 后期完善此方法，需要适配 某采购订单 供应商订单退款方式
        WmsAccountChargeV2Req request = WmsAccountChargeV2Req.builder()
            .orderNo(orderContextDTO.getPurchaseOrderNo())
            .cusCode(cusCode)
            .operationType(WmsOperationTypeEnum.REFUND)
            .refundAmount(orderContextDTO.getCustomerTotalAmount())
            .refundReason("订单取消，返还账户余额")
            .build();
        return Optional.ofNullable(wmsApiClient.refundAccountInfoByPurchase(request));
    }

    @Override
    public Optional<List<WmsCreatePurchaseOrderRes>> createWmsPurchaseOrderNew(OrderContextDTO orderContextDTO, String cusCode) {
        log.info("开始构建 WMS 采购订单请求，采购单号: {}", orderContextDTO.getPurchaseOrder().getPurchaseOrderNo());

        try {
            // 1. 构建 WMS 订单列表
            List<WmsPurchaseOrderReq> wmsOrders = buildWmsOrders(orderContextDTO);

            // 2. 构建 WMS API 请求体
            WmsCreateOrderReq wmsCreateOrderRequest = buildWmsCreateOrderRequest(wmsOrders, cusCode);

            // 3. 调用 WMS API
            List<WmsCreatePurchaseOrderRes> response = wmsApiClient.createPurchaseOrderNew(wmsCreateOrderRequest);

            log.info("WMS采购订单创建成功，采购单号: {} , request: {}", orderContextDTO.getPurchaseOrder().getPurchaseOrderNo(), JacksonUtil.toJsonString(wmsCreateOrderRequest));
            return Optional.of(response);
        } catch (WmsApiException e) {
            log.error("WMS采购订单创建失败，采购单号: {}, API调用异常: {}",
                orderContextDTO.getPurchaseOrder().getPurchaseOrderNo(), e.getMessage(), e);
        } catch (Exception e) {
            log.error("WMS采购订单创建失败，采购单号: {}, 业务处理异常: {}",
                orderContextDTO.getPurchaseOrder().getPurchaseOrderNo(), e.getMessage(), e);
        }
        return Optional.empty();
    }

    @Override
    public void payWmsPurchaseOrder(OrderContextDTO orderContextDTO, String cusCode) {
        log.info("开始构建 WMS 采购订单请求，采购单号: {}", orderContextDTO.getPurchaseOrder().getPurchaseOrderNo());
        // 1. 获取账户信息，查看余额
        // 1.1 余额不足，扣款失败
        // 2. 发起扣款请求， 采购单号、总金额(运费 5、服务费 1.5、商品金额 150) 156.5
        // 2.1 扣款成功，修改订单状态为已支付
        // 2.2 扣款失败，回滚处理。
        // 2.3 记录账上扣款失败 流水账
    }

    @Override
    public void updateWmsPurchaseOrder(OrderContextDTO orderContextDTO) {
        try {
            log.info("开始更新 WMS 采购订单请求，采购单号: {}", orderContextDTO.getPurchaseOrder().getPurchaseOrderNo());
            List<WmsPurchaseOrderDetailReq> request = buildUpdateWmsOrderDetails(orderContextDTO);
            this.wmsApiClient.updatePurchaseOrder(request);
        } catch (Exception e) {
            log.error("WMS采购订单更新失败，采购单号: {}, 业务处理异常: {}",
                orderContextDTO.getPurchaseOrder().getPurchaseOrderNo(), e.getMessage(), e);
            throw BusinessExceptionI18n.of(FulfillmenErrorCodeEnum.API_CALL_FAILED, "updateWmsPurchaseOrder", "更新采购单失败");
        }
    }

    @Override
    public void updateWmsPurchaseOrder(WmsPurchaseOrderDetailReq request) {
        this.updateWmsPurchaseOrder(Collections.singletonList(request));
    }

    @Override
    public void updateWmsPurchaseOrder(List<WmsPurchaseOrderDetailReq> request) {
        try {
            log.info("开始更新 WMS 采购订单请求，采购单号: {}", JacksonUtil.toJsonString(request));
            this.wmsApiClient.updatePurchaseOrder(request);
        } catch (Exception e) {
            log.error("WMS采购订单更新失败，业务处理异常: {}", e.getMessage(), e);
            throw BusinessExceptionI18n.of(FulfillmenErrorCodeEnum.API_CALL_FAILED, "updateWmsPurchaseOrder", "更新采购单失败");
        }
    }

    @Override
    public void cancelWmsPurchaseOrder(String purchaseOrderNo, String cusCode) {
        try {
            // 查询采购订单列表
            List<WmsPurchaseOrderDetailsRes> wmsPurchaseOrderDetailsRes = this.wmsApiClient.queryOrderDetail(PurchaseOrderDetailReq.builder().purchaseNo(purchaseOrderNo).build());
            // 如果不为空
            if (CollectionUtils.isEmpty(wmsPurchaseOrderDetailsRes)) {
                log.warn("wms 订单获取失败 : [{}] ", purchaseOrderNo);
                return;
            }
            // 取消采购单下的所有供应商订单
            List<WmsPurchaseOrderDetailReq> wmsPurchaseOrderDetailReqs = wmsPurchaseOrderDetailsRes.stream().map(wmsPurchaseOrderDetailsRes1 -> WmsPurchaseOrderDetailReq.builder()
                .purchaseNo(wmsPurchaseOrderDetailsRes1.getPurchaseNo())
                .shopOrderId(wmsPurchaseOrderDetailsRes1.getShopOrderId())
                .status(WmsOrderStatusEnum.CANCELED)
                .build()).collect(Collectors.toList());
            // 取消
            this.wmsApiClient.updatePurchaseOrder(wmsPurchaseOrderDetailReqs);
        } catch (Exception e) {
            log.error("WMS采购订单更新失败，采购单号: {}, 业务处理异常: {}",
                purchaseOrderNo, e.getMessage(), e);
            throw BusinessExceptionI18n.of(FulfillmenErrorCodeEnum.API_CALL_FAILED, "updateWmsPurchaseOrder", "更新采购单失败");
        }
    }

    @Override
    public void cancelWmsPurchaseOrderByNayaPurchaseNo(String nayaPurchaseOrderNo, String cusCode) {
        try {
            // 查询采购订单列表
            List<WmsPurchaseOrderDetailsRes> wmsPurchaseOrderDetailsRes = this.wmsApiClient.queryOrderDetail(PurchaseOrderDetailReq.builder().nayaPurchaseNo(nayaPurchaseOrderNo).build());
            // 如果不为空
            if (CollectionUtils.isEmpty(wmsPurchaseOrderDetailsRes)) {
                log.warn("对应的采购订单不存在 : [{}] ", nayaPurchaseOrderNo);
                return;
            }
            // 过滤还未取消的订单状态
            List<WmsPurchaseOrderDetailsRes> wmsPurchaseOrderDetailsResByPendingCancel = wmsPurchaseOrderDetailsRes
                .stream().filter(wmsPurchaseOrderDetailsRes1 -> !wmsPurchaseOrderDetailsRes1.getStatus().equals(WmsOrderStatusEnum.CANCELED))
                .toList();

            // 取消采购单下的所有供应商订单
            if (CollectionUtils.isEmpty(wmsPurchaseOrderDetailsResByPendingCancel)) {
                log.info("暂无取消的订单 : [{}] ", nayaPurchaseOrderNo);
                return;
            }
            List<WmsPurchaseOrderDetailReq> wmsPurchaseOrderDetailReqs = Lists.newArrayList();
            wmsPurchaseOrderDetailsResByPendingCancel.forEach(wmsDetailsRes -> {
                WmsPurchaseOrderDetailReq wmsPurchaseOrderDetailReq = WmsPurchaseOrderDetailReq.builder()
                    .purchaseNo(wmsDetailsRes.getPurchaseNo())
                    .shopOrderId(wmsDetailsRes.getShopOrderId())
                    .status(WmsOrderStatusEnum.CANCELED)
                    .build();
                wmsPurchaseOrderDetailReqs.add(wmsPurchaseOrderDetailReq);
            });
            // 取消
            this.wmsApiClient.updatePurchaseOrder(wmsPurchaseOrderDetailReqs);
        } catch (Exception e) {
            log.error("WMS采购订单更新失败，采购单号: {}, 业务处理异常: {}",
                nayaPurchaseOrderNo, e.getMessage(), e);
            throw BusinessExceptionI18n.of(FulfillmenErrorCodeEnum.API_CALL_FAILED, "updateWmsPurchaseOrder", "更新采购单失败");
        }
    }

    /**
     * 构建 WMS API 请求的主体对象
     *
     * @param wmsOrders WMS 订单列表
     * @param cusCode   WMS cusCode
     * @return WMS 创建订单的请求对象
     */
    private WmsCreateOrderReq buildWmsCreateOrderRequest(List<WmsPurchaseOrderReq> wmsOrders, String cusCode) {
        WmsCreateOrderReq request = new WmsCreateOrderReq();
        request.setCustomerCode(cusCode);
        request.setOrders(wmsOrders);

        return request;
    }

    /**
     * 将内部的供应商订单列表转换为 WMS 订单列表
     *
     * @param orderContextDTO 订单上下文
     * @return WMS 订单列表
     */
    private List<WmsPurchaseOrderReq> buildWmsOrders(OrderContextDTO orderContextDTO) {
        // 将所有订单项按其所属的供应商订单ID进行分组，便于查找
        Map<Long, List<TzOrderItem>> itemsBySupplierOrder = orderContextDTO.getOrderItems().stream()
            .collect(Collectors.groupingBy(TzOrderItem::getSupplierOrderId));
        TzOrderPurchase purchaseOrder = orderContextDTO.getPurchaseOrder();
        // 遍历每一个供应商订单，将其转换为WMS订单
        return orderContextDTO.getSupplierOrders().stream()
            .map(supplierOrder -> {
                List<TzOrderItem> orderItems = itemsBySupplierOrder.get(supplierOrder.getId());
                return WmsPurchaseOrderConvert.INSTANCE.initWmsCreateOrderRequest(purchaseOrder, supplierOrder, orderItems);
            })
          .toList();
    }

    /**
     * 将内部的供应商订单列表转换为 WMS 订单列表
     *
     * @param orderContextDTO 订单上下文
     * @return WMS 订单列表
     */
    private List<WmsPurchaseOrderDetailReq> buildUpdateWmsOrderDetails(OrderContextDTO orderContextDTO) {
        // 将所有订单项按其所属的供应商订单ID进行分组，便于查找
        Map<Long, List<TzOrderItem>> itemsBySupplierOrder = orderContextDTO.getOrderItems().stream()
            .collect(Collectors.groupingBy(TzOrderItem::getSupplierOrderId));
        TzOrderPurchase purchaseOrder = orderContextDTO.getPurchaseOrder();
        // 遍历每一个供应商订单，将其转换为WMS订单
        return orderContextDTO.getSupplierOrders().stream()
            .map(supplierOrder -> {
                List<TzOrderItem> orderItems = itemsBySupplierOrder.get(supplierOrder.getId());
                return WmsPurchaseOrderConvert.INSTANCE.toWmsPurchaseOrderDetailReq(purchaseOrder, supplierOrder, orderItems);
            })
          .toList();
    }

    /**
     * WMS 订单查询
     *
     * @param request WMS订单查询请求
     * @return WMS订单查询响应
     */
    @Override
    public Optional<WmsPageDTO<WmsPurchaseOrderInfoRes>> queryOrder(WmsOrderQueryReq request) {
        log.info("开始WMS订单查询，查询条件: {}", request);

        try {
            // 1. 获取当前登录用户的WMS账户信息
            TzUser currentUser = tzUserMapper.selectById(UserContextHolder.getUserId());
            if (currentUser == null || currentUser.getWmsCusCode() == null) {
                throw BusinessExceptionI18n.of(FulfillmenErrorCodeEnum.UNABLE_RETRIEVE_WMS_ACCOUNT);
            }

            // 4. 调用WMS API
            WmsPageDTO<WmsPurchaseOrderInfoRes> response = wmsApiClient.queryOrder(request);

            log.info("WMS订单查询成功");
            return Optional.of(response);
        } catch (WmsApiException e) {
            log.error("WMS订单查询失败，API调用异常: {}", e.getMessage(), e);
            return Optional.empty();
        } catch (Exception e) {
            log.error("WMS订单查询失败，业务处理异常: {}", e.getMessage(), e);
            return Optional.empty();
        }
    }

    @Override
    public List<WmsPurchaseOrderDetailsRes> queryOrderDetail(PurchaseOrderDetailReq request) {
        log.info("开始WMS订单详情查询，查询条件: {}", JacksonUtil.toJsonString(request));
        try {
            return wmsApiClient.queryOrderDetail(request);
        } catch (WmsApiException ex) {
            log.error("wms 订单查询失败 : [{}] ", request, ex);
            return Lists.newArrayList();
        } catch (Exception e) {
            log.error("WMS订单详情查询失败，业务处理异常: {}", e.getMessage(), e);
            throw BusinessExceptionI18n.of(FulfillmenErrorCodeEnum.API_CALL_FAILED, "queryOrderDetail", "查询订单详情失败");
        }
    }

    // =================================================================================================================
    // 入库单处理
    // =================================================================================================================

    /**
     * 创建入库单 根据采购订单
     *
     * @param wmsPurchaseOrderDetailsRes WMS采购订单详情响应
     * @return WMS API 的响应
     */
    @Override
    public WmsCreateInboundOrderRes inboundCreateByPurchase(WmsPurchaseOrderDetailsRes wmsPurchaseOrderDetailsRes) {
        log.info("开始创建入库单，请求参数: {}", JacksonUtil.toJsonString(wmsPurchaseOrderDetailsRes));
        try {
            List<WmsPurchaseOrderDetailsItemRes> orderDetailsItemRes = wmsPurchaseOrderDetailsRes.getOrderDetailsItemRes();
            WmsCreateInboundOrderReq request = WmsCreateInboundOrderReq.builder()
                .cusCode(wmsPurchaseOrderDetailsRes.getCusCode())
                .purchaseNo(wmsPurchaseOrderDetailsRes.getPurchaseNo())
                .trackingNo(wmsPurchaseOrderDetailsRes.getTrackingNo())
                .deliveryTime(wmsPurchaseOrderDetailsRes.getShippingTime())
                // 仓库 ID
                .warehouseAddress(wmsPurchaseOrderDetailsRes.getStoreId())
                // 装箱数
                .enchaseNum(1)
                .shipmentType(WmsAsnShipmentTypeEnums.ONE_PIECE_SALE)
                .remark(wmsPurchaseOrderDetailsRes.getRemark())
                .purchaseDetails(orderDetailsItemRes.stream().map(item -> WmsCreateInboundOrderReq.PurchaseDetailsDTO.builder()
                    .sku(item.getSkuId())
                    .productId(Long.valueOf(item.getProductId()))
                    .quantity(item.getQuantity())
                    .unitPrice(item.getUnitPrice())
                    .weight(item.getWeight())
                    .cnName(item.getCnName())
                    .enName(item.getEnName())
                    .skuAttributes(item.getSkuAttrib())
                    .build()).collect(Collectors.toList()))
                .build();
            return wmsApiClient.inboundCreateByPurchase(request);
        } catch (Exception e) {
            log.error("创建入库单失败, 异常: {}", e.getMessage(), e);
        }
        return null;
    }

    @Override
    public WmsInboundUpdateRes inboundUpdate(WmsInboundOrderUpdateReq request) {
        log.info("开始更新入库单，请求参数: {}", request);
        try {
            return wmsApiClient.inboundUpdateByPurchase(request);
        } catch (Exception e) {
            log.error("更新入库单失败，请求参数: {}, 异常: {}", request, e.getMessage(), e);
        }
        return null;
    }

    @Override
    public List<WmsInboundInfoRes> inboundInfo(WmsInboundInfoReq request) {
        log.info("开始查询入库单详情，请求参数: {}", request);
        try {
            return wmsApiClient.inboundQuery(request);
        } catch (Exception e) {
            log.error("查询入库单详情失败，请求参数: {}, 异常: {}", request, e.getMessage(), e);
        }
        return Collections.emptyList();
    }

}
