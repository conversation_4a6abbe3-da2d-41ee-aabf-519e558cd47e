/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.product.strategy.pricing.impl;

import com.fulfillmen.shop.common.context.UserContextHolder;
import com.fulfillmen.shop.manager.product.context.ProductProcessingContext;
import com.fulfillmen.shop.manager.product.context.UserPricingContext;
import com.fulfillmen.shop.manager.product.strategy.pricing.PricingStrategy;
import java.math.BigDecimal;
import java.math.RoundingMode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 用户定价策略
 *
 * <pre>
 * 核心职责：
 * 1. 为已登录用户提供基于用户服务费率的定价
 * 2. 使用 UserContextHolder.getServiceFeeRate() 获取用户费率
 * 3. 优先级高于租户定价策略
 * 4. 支持用户个性化的定价配置
 *
 * 适用场景：
 * - 已登录的用户
 * - 有个人WMS绑定的用户
 * - 需要个性化定价的用户
 *
 * 定价逻辑：
 * - 原价 + (原价 × 用户服务费率) = 最终价格
 * - 如果用户没有绑定WMS账户，会回退到租户服务费率
 * - 支持用户级别的定价个性化配置
 * </pre>
 *
 * <AUTHOR>
 * @date 2025/08/22
 * @since 1.0.0
 */
@Slf4j
@Component
public class UserPricingStrategy implements PricingStrategy {

    /**
     * 价格精度（小数位数）
     */
    private static final int DECIMAL_PLACES = 2;

    @Override
    public BigDecimal calculatePrice(BigDecimal originalPrice, UserPricingContext context) {
        if (originalPrice == null || originalPrice.compareTo(BigDecimal.ZERO) <= 0) {
            log.warn("用户定价策略: 原始价格无效，返回零价格: {}", originalPrice);
            return BigDecimal.ZERO;
        }

        try {
            // 获取用户服务费率
            BigDecimal userServiceFeeRate = UserContextHolder.getServiceFeeRate();
            Long userId = UserContextHolder.getUserId();

            // 计算加价后的价格
            BigDecimal markup = originalPrice.multiply(userServiceFeeRate);
            BigDecimal finalPrice = originalPrice.add(markup);

            // 应用价格精度
            finalPrice = finalPrice.setScale(DECIMAL_PLACES, RoundingMode.HALF_UP);

            // 记录定价日志
            if (log.isDebugEnabled()) {
                log.debug("用户定价策略计算: 用户ID={}, 原价={}, 服务费率={}%, 加价={}, 最终价格={}",
                  userId, originalPrice, userServiceFeeRate.multiply(new BigDecimal("100")), markup, finalPrice);
            }

            return finalPrice;

        } catch (Exception e) {
            log.error("用户定价策略计算异常: 原价={}, 用户ID={}", originalPrice, UserContextHolder.getUserId(), e);
            // 异常时返回原价，确保系统可用性
            return originalPrice;
        }
    }

    @Override
    public boolean isApplicable(ProductProcessingContext context) {
        // 用户定价策略适用于已登录用户
        boolean hasUserContext = UserContextHolder.hasUserContext();
        Long userId = UserContextHolder.getUserId();

        if (log.isDebugEnabled()) {
            log.debug("用户定价策略适用性检查: 有用户上下文={}, 用户ID={}", hasUserContext, userId);
        }

        return hasUserContext && userId != null;
    }

    @Override
    public PricingStrategyType getPricingType() {
        // 使用CUSTOM表示用户个性化定价
        return PricingStrategyType.CUSTOM;
    }

    @Override
    public int getPriority() {
        // 优先级高于租户策略，低于VIP等特殊策略
        return 5;
    }

    @Override
    public String getStrategyName() {
        return "USER_PRICING";
    }

    @Override
    public String getPriceAdjustmentDescription(BigDecimal originalPrice,
      BigDecimal finalPrice,
      UserPricingContext context) {
        if (originalPrice == null || finalPrice == null) {
            return "价格信息不完整";
        }

        BigDecimal userServiceFeeRate = UserContextHolder.getServiceFeeRate();
        BigDecimal markupPercentage = userServiceFeeRate.multiply(new BigDecimal("100"));
        Long userId = UserContextHolder.getUserId();
        String username = UserContextHolder.getUsername();

        return String.format("应用用户定价策略 (用户: %s-%s)，服务费率%.1f%% (%.2f → %.2f)", userId, username, markupPercentage, originalPrice, finalPrice);
    }

    @Override
    public boolean validatePrice(BigDecimal originalPrice,
      BigDecimal calculatedPrice,
      UserPricingContext context) {
        // 基础验证
        if (originalPrice == null || calculatedPrice == null) {
            return false;
        }

        if (calculatedPrice.compareTo(BigDecimal.ZERO) <= 0) {
            return false;
        }

        // 验证价格变化是否合理（服务费率应该在合理范围内）
        BigDecimal actualMarkupRate = calculatedPrice.subtract(originalPrice)
          .divide(originalPrice, 4, RoundingMode.HALF_UP);

        // 用户服务费率应该在 0% 到 100% 之间
        boolean isValidUserPrice = actualMarkupRate.compareTo(BigDecimal.ZERO) >= 0 &&
          actualMarkupRate.compareTo(BigDecimal.ONE) <= 0;

        if (!isValidUserPrice) {
            log.warn("用户定价验证失败: 用户ID={}, 计算的加价率{}超出合理范围[0%, 100%]",
              UserContextHolder.getUserId(), actualMarkupRate.multiply(new BigDecimal("100")));
        }

        return isValidUserPrice;
    }

    /**
     * 获取当前用户的服务费率信息
     *
     * @return 服务费率信息字符串
     */
    public String getUserServiceFeeInfo() {
        BigDecimal serviceFeeRate = UserContextHolder.getServiceFeeRate();
        Long userId = UserContextHolder.getUserId();
        String username = UserContextHolder.getUsername();
        String wmsCusCode = UserContextHolder.getWmsCusCode();

        return String.format("用户定价配置 - 用户ID: %s, 用户名: %s, WMS客户码: %s, 服务费率: %.1f%%",
          userId, username, wmsCusCode, serviceFeeRate.multiply(new BigDecimal("100")));
    }

    /**
     * 检查用户是否有WMS绑定
     *
     * @return true-有绑定，false-无绑定
     */
    public boolean hasWmsBinding() {
        try {
            String wmsCusCode = UserContextHolder.getWmsCusCode();
            return wmsCusCode != null && !wmsCusCode.trim().isEmpty();
        } catch (Exception e) {
            log.error("检查用户WMS绑定异常: 用户ID={}", UserContextHolder.getUserId(), e);
            return false;
        }
    }

    /**
     * 检查用户定价策略是否可用
     *
     * @return true-可用，false-不可用
     */
    public boolean isUserPricingAvailable() {
        try {
            return UserContextHolder.hasUserContext() &&
              UserContextHolder.getUserId() != null &&
              UserContextHolder.getServiceFeeRate() != null;
        } catch (Exception e) {
            log.error("检查用户定价可用性异常", e);
            return false;
        }
    }
}
