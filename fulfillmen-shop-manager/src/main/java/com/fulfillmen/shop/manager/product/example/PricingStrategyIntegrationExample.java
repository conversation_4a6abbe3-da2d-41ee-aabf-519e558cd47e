/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.product.example;

import com.fulfillmen.shop.domain.dto.TzProductDTO;
import com.fulfillmen.shop.domain.dto.TzProductSkuDTO;
import com.fulfillmen.shop.manager.product.context.ProductProcessingContext;
import com.fulfillmen.shop.manager.product.service.IProductStrategyService;
import com.fulfillmen.shop.manager.product.strategy.ProductStrategyFactory;
import com.fulfillmen.shop.manager.product.strategy.pricing.PricingStrategy;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 定价策略集成示例
 *
 * <pre>
 * 演示如何使用重构后的定价策略系统：
 * 1. 自动选择定价策略（用户登录状态优先，否则使用租户策略）
 * 2. 应用服务费率进行价格计算
 * 3. 集成到产品服务流程中
 * </pre>
 *
 * <AUTHOR>
 * @date 2025/08/22
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class PricingStrategyIntegrationExample {

    private final IProductStrategyService productStrategyService;
    private final ProductStrategyFactory strategyFactory;

    /**
     * 示例：智能定价策略应用 根据用户登录状态自动选择合适的定价策略
     */
    public TzProductDTO applySmartPricing(TzProductDTO product) {
        try {
            // 获取智能定价策略（自动选择用户或租户策略）
            PricingStrategy pricingStrategy = strategyFactory.getSmartPricingStrategy();

            if (pricingStrategy == null) {
                log.warn("未找到适用的定价策略，返回原始产品");
                return product;
            }

            // 构建处理上下文
            ProductProcessingContext context = ProductProcessingContext.builder()
              .productId(product.getId())
              .platformProductId(product.getPdcPlatformProductId())
              .build();

            // 应用定价策略
            TzProductDTO processedProduct = pricingStrategy.process(product, context);

            log.info("智能定价完成: 策略={}, 产品ID={}",
              pricingStrategy.getStrategyName(), product.getId());

            return processedProduct != null ? processedProduct : product;

        } catch (Exception e) {
            log.error("智能定价异常: 产品ID={}", product.getId(), e);
            return product;
        }
    }

    /**
     * 示例：批量产品定价 对产品列表应用统一的定价策略
     */
    public void applyBatchPricing(List<TzProductDTO> products) {
        if (products == null || products.isEmpty()) {
            return;
        }

        // 获取智能定价策略
        PricingStrategy pricingStrategy = strategyFactory.getSmartPricingStrategy();
        if (pricingStrategy == null) {
            log.warn("未找到适用的定价策略，跳过批量定价");
            return;
        }

        log.info("开始批量定价: 策略={}, 产品数量={}",
          pricingStrategy.getStrategyName(), products.size());

        int successCount = 0;
        for (TzProductDTO product : products) {
            try {
                ProductProcessingContext context = ProductProcessingContext.builder()
                  .productId(product.getId())
                  .platformProductId(product.getPdcPlatformProductId())
                  .build();

                TzProductDTO processedProduct = pricingStrategy.process(product, context);
                if (processedProduct != null) {
                    successCount++;
                }
            } catch (Exception e) {
                log.error("批量定价异常: 产品ID={}", product.getId(), e);
            }
        }

        log.info("批量定价完成: 成功={}/{}", successCount, products.size());
    }

    /**
     * 示例：创建测试产品数据
     */
    public TzProductDTO createTestProduct() {
        TzProductDTO product = new TzProductDTO();
        product.setId(1L);
        product.setTitle("定价策略测试产品");
        product.setPdcPlatformProductId("TEST_PRODUCT_123");

        // 创建测试SKU
        TzProductSkuDTO sku1 = new TzProductSkuDTO();
        sku1.setId(1L);
        sku1.setSku("TEST_SKU_1");
        sku1.setPrice(new BigDecimal("100.00"));
        sku1.setDropShippingPrice(new BigDecimal("100.00"));

        TzProductSkuDTO sku2 = new TzProductSkuDTO();
        sku2.setId(2L);
        sku2.setSku("TEST_SKU_2");
        sku2.setPrice(new BigDecimal("200.00"));
        sku2.setDropShippingPrice(new BigDecimal("200.00"));

        product.setSkuList(Arrays.asList(sku1, sku2));

        return product;
    }

    /**
     * 示例：获取定价策略信息
     */
    public void displayPricingInfo() {
        PricingStrategy strategy = strategyFactory.getSmartPricingStrategy();
        if (strategy != null) {
            log.info("当前定价策略: {}", strategy.getStrategyName());
            log.info("策略优先级: {}", strategy.getPriority());
            log.info("策略类型: {}", strategy.getPricingType());
        } else {
            log.warn("未找到可用的定价策略");
        }

        // 显示系统状态
        log.info("产品策略系统状态:\n{}", productStrategyService.getSystemStatus());
        log.info("执行统计:\n{}", productStrategyService.getExecutionStatistics());
    }

    /**
     * 完整示例：从产品创建到定价的完整流程
     */
    public void fullIntegrationExample() {
        log.info("=== 定价策略集成完整示例 ===");

        // 1. 显示当前策略信息
        displayPricingInfo();

        // 2. 创建测试产品
        TzProductDTO testProduct = createTestProduct();
        log.info("创建测试产品: ID={}, SKU数量={}",
          testProduct.getId(), testProduct.getSkuList().size());

        // 3. 应用智能定价
        TzProductDTO pricedProduct = applySmartPricing(testProduct);

        // 4. 显示定价结果
        if (pricedProduct.getSkuList() != null) {
            pricedProduct.getSkuList().forEach(sku -> log.info("SKU定价结果: SKU={}, 原价={}, 最终价={}",
              sku.getSku(), sku.getPrice(), sku.getDropShippingPrice()));
        }

        // 5. 显示最终统计
        log.info("定价策略集成示例完成");
        log.info("执行统计:\n{}", productStrategyService.getExecutionStatistics());
    }
}
