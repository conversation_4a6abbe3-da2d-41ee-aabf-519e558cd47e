/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.product.config;

import java.math.BigDecimal;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 产品策略配置类
 *
 * <pre>
 * 配置项说明：
 * 1. 定价策略相关配置
 * 2. 处理器链相关配置
 * 3. 缓存策略相关配置
 *
 * 配置文件示例（application.yml）：
 * fulfillmen:
 *   product:
 *     strategy:
 *       enabled: true
 *       pricing:
 *         default-markup-rate: 0.15
 *         decimal-places: 2
 *       processor:
 *         chain-enabled: true
 *         statistics-enabled: true
 * </pre>
 *
 * <AUTHOR>
 * @date 2025/08/22
 * @since 1.0.0
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "fulfillmen.product.strategy")
public class ProductStrategyConfig {

    /**
     * 是否启用产品策略系统
     */
    private boolean enabled = true;

    /**
     * 定价策略配置
     */
    private PricingConfig pricing = new PricingConfig();

    /**
     * 处理器配置
     */
    private ProcessorConfig processor = new ProcessorConfig();

    /**
     * 缓存配置
     */
    private CacheConfig cache = new CacheConfig();

    /**
     * 定价策略配置
     */
    @Data
    public static class PricingConfig {

        /**
         * 默认加价率
         */
        private BigDecimal defaultMarkupRate = new BigDecimal("0.15");

        /**
         * 最小加价率
         */
        private BigDecimal minMarkupRate = new BigDecimal("0.05");

        /**
         * 最大加价率
         */
        private BigDecimal maxMarkupRate = new BigDecimal("0.50");

        /**
         * 价格精度（小数位数）
         */
        private int decimalPlaces = 2;

    }

    /**
     * 处理器配置
     */
    @Data
    public static class ProcessorConfig {

        /**
         * 是否启用处理器链
         */
        private boolean chainEnabled = true;

        /**
         * 是否启用并行处理
         */
        private boolean parallelProcessing = false;

        /**
         * 处理超时时间（毫秒）
         */
        private long processingTimeoutMs = 10000;

        /**
         * 是否启用处理器统计
         */
        private boolean statisticsEnabled = true;
    }

    /**
     * 缓存配置
     */
    @Data
    public static class CacheConfig {

        /**
         * 是否启用策略缓存
         */
        private boolean enabled = true;

        /**
         * 缓存过期时间（分钟）
         */
        private int expireMinutes = 30;

        /**
         * 最大缓存条目数
         */
        private int maxEntries = 10000;

        /**
         * 是否启用缓存统计
         */
        private boolean statisticsEnabled = true;
    }

    // ==================== 便捷方法 ====================

    /**
     * 检查定价策略是否启用
     */
    public boolean isPricingEnabled() {
        return enabled && pricing != null;
    }

    /**
     * 检查处理器链是否启用
     */
    public boolean isProcessorChainEnabled() {
        return enabled && processor != null && processor.isChainEnabled();
    }

    /**
     * 检查缓存是否启用
     */
    public boolean isCacheEnabled() {
        return enabled && cache != null && cache.isEnabled();
    }

    /**
     * 获取配置摘要
     */
    public String getConfigurationSummary() {
        return String.format(
          "产品策略配置摘要: 总开关=%s, 定价策略=%s, 处理器链=%s, 缓存=%s",
          enabled,
          isPricingEnabled(),
          isProcessorChainEnabled(),
          isCacheEnabled()
        );
    }

    /**
     * 获取定价配置详情
     */
    public String getPricingConfigDetails() {
        if (!isPricingEnabled()) {
            return "定价策略已禁用";
        }

        return String.format(
          "定价配置: 默认加价率=%.1f%%, 范围=[%.1f%%, %.1f%%], 精度=%d位",
          pricing.getDefaultMarkupRate().multiply(new BigDecimal("100")),
          pricing.getMinMarkupRate().multiply(new BigDecimal("100")),
          pricing.getMaxMarkupRate().multiply(new BigDecimal("100")),
          pricing.getDecimalPlaces()
        );
    }

    /**
     * 验证配置的合理性
     */
    public boolean validateConfiguration() {
        try {
            // 验证定价配置
            if (isPricingEnabled()) {
                if (pricing.getDefaultMarkupRate().compareTo(pricing.getMinMarkupRate()) < 0 ||
                  pricing.getDefaultMarkupRate().compareTo(pricing.getMaxMarkupRate()) > 0) {
                    return false;
                }

                if (pricing.getDecimalPlaces() < 0 || pricing.getDecimalPlaces() > 6) {
                    return false;
                }
            }

            // 验证处理器配置
            if (isProcessorChainEnabled()) {
                if (processor.getProcessingTimeoutMs() <= 0) {
                    return false;
                }
            }

            // 验证缓存配置
            if (isCacheEnabled()) {
                if (cache.getExpireMinutes() <= 0 || cache.getMaxEntries() <= 0) {
                    return false;
                }
            }

            return true;

        } catch (Exception e) {
            return false;
        }
    }
}
