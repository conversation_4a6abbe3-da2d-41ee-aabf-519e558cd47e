/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.product.context;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;

/**
 * 用户定价上下文
 *
 * <pre>
 * 核心职责：
 * 1. 承载用户相关的定价信息和偏好
 * 2. 支持个性化定价策略的参数传递
 * 3. 包含用户等级、折扣、历史购买等信息
 * 4. 为定价策略提供决策依据
 *
 * 应用场景：
 * - VIP用户专享折扣
 * - 基于购买历史的动态定价
 * - 用户等级差异化定价
 * - 促销活动的用户筛选
 * </pre>
 *
 * <AUTHOR>
 * @date 2025/08/22
 * @since 1.0.0
 */
@Data
@Builder
public class UserPricingContext {

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户等级
     */
    @Builder.Default
    private UserLevel userLevel = UserLevel.NORMAL;

    /**
     * 用户折扣率 (0.0-1.0) 例如：0.9表示9折，0.85表示8.5折
     */
    @Builder.Default
    private BigDecimal discountRate = BigDecimal.ONE;

    /**
     * 用户专属加价率 (基于成本的加价百分比) 例如：0.15表示15%加价，0.20表示20%加价
     */
    @Builder.Default
    private BigDecimal markupRate = new BigDecimal("0.15");

    /**
     * 是否为VIP用户
     */
    @Builder.Default
    private boolean isVip = false;

    /**
     * VIP等级 (1-5级，5级最高)
     */
    @Builder.Default
    private Integer vipLevel = 0;

    /**
     * 用户累计购买金额
     */
    @Builder.Default
    private BigDecimal totalPurchaseAmount = BigDecimal.ZERO;

    /**
     * 用户累计订单数量
     */
    @Builder.Default
    private Integer totalOrderCount = 0;

    /**
     * 最后购买时间
     */
    private LocalDateTime lastPurchaseTime;

    /**
     * 用户首选货币
     */
    @Builder.Default
    private String preferredCurrency = "CNY";

    /**
     * 用户所在地区
     */
    private String region;

    /**
     * 是否享受特殊定价
     */
    @Builder.Default
    private boolean hasSpecialPricing = false;

    /**
     * 特殊定价备注
     */
    private String specialPricingNote;

    /**
     * 用户等级枚举
     */
    @Getter
    public enum UserLevel {

        /**
         * 普通用户
         */
        NORMAL("普通用户", new BigDecimal("0.15")),

        /**
         * 银牌用户
         */
        SILVER("银牌用户", new BigDecimal("0.12")),

        /**
         * 金牌用户
         */
        GOLD("金牌用户", new BigDecimal("0.10")),

        /**
         * 白金用户
         */
        PLATINUM("白金用户", new BigDecimal("0.08")),

        /**
         * 钻石用户
         */
        DIAMOND("钻石用户", new BigDecimal("0.05"));

        private final String description;
        private final BigDecimal defaultMarkupRate;

        UserLevel(String description, BigDecimal defaultMarkupRate) {
            this.description = description;
            this.defaultMarkupRate = defaultMarkupRate;
        }

    }

    /**
     * 获取实际应用的加价率 优先使用用户专属加价率，如果没有则使用等级默认加价率
     *
     * @return 实际加价率
     */
    public BigDecimal getEffectiveMarkupRate() {
        if (markupRate != null && markupRate.compareTo(BigDecimal.ZERO) > 0) {
            return markupRate;
        }
        return userLevel.getDefaultMarkupRate();
    }

    /**
     * 判断是否为高价值用户
     *
     * @return true-高价值用户，false-普通用户
     */
    public boolean isHighValueUser() {
        return isVip ||
          userLevel.ordinal() >= UserLevel.GOLD.ordinal() ||
          (totalPurchaseAmount != null && totalPurchaseAmount.compareTo(new BigDecimal("10000")) >= 0);
    }

    /**
     * 判断是否为新用户
     *
     * @return true-新用户，false-老用户
     */
    public boolean isNewUser() {
        return totalOrderCount == null || totalOrderCount == 0;
    }
}
