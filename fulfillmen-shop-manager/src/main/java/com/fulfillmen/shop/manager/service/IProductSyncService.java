/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.service;

import com.fulfillmen.shop.domain.dto.TzProductDTO;
import com.fulfillmen.shop.domain.dto.product.alibaba.AlibabaProductDetailDTO;
import com.fulfillmen.shop.domain.entity.TzProductSku;
import com.fulfillmen.shop.manager.service.dto.BatchSyncResult;
import java.math.BigDecimal;
import java.util.List;

/**
 * 产品同步服务接口
 *
 * <p>
 * 职责定义：
 * 1. 负责所有产品数据的同步逻辑（从PdcProductMapping到TzProductSpu/TzProductSku）
 * 2. 提供统一的产品数据获取入口（优先TzProduct，否则自动同步）
 * 3. 处理单品默认SKU的创建和维护
 * 4. 数据修复和验证功能
 * </p>
 *
 * <AUTHOR>
 * @date 2025/6/13 23:59
 * @since 1.0.0
 */
public interface IProductSyncService {

    // ==================== 核心同步方法 ====================

    /**
     * 根据平台产品ID同步产品数据
     *
     * <p>
     * 核心逻辑：
     * 1. 检查TzProductSpu是否已存在
     * 2. 不存在则从PdcProductMapping获取数据并同步
     * 3. 创建对应的TzProductSku数据
     * 4. 返回完整的TzProductDTO
     * </p>
     *
     * @param platformProductId 平台产品ID（如1688的offerId）
     * @return 同步后的产品DTO，失败返回null
     */
    TzProductDTO syncProductByPlatformId(String platformProductId);

    /**
     * 强制重新同步产品数据
     *
     * @param platformProductId 平台产品ID
     * @param forceUpdate       是否强制更新已存在的数据
     * @return 重新同步后的产品DTO
     */
    TzProductDTO resyncProductByPlatformId(String platformProductId, boolean forceUpdate);

    /**
     * 批量重新同步产品数据
     *
     * <p>
     * 核心特性： 1. 使用虚拟线程并发处理，提升批量同步性能 2. 支持部分失败容错，不会因为单个产品失败而影响整体 3. 详细的成功/失败统计和日志记录 4. 自动过滤重复的platformProductId
     * </p>
     *
     * @param platformProductIds 平台产品ID列表
     * @param forceUpdate        是否强制更新已存在的数据
     * @return 批量同步结果，包含成功和失败的产品信息
     */
    BatchSyncResult batchResyncProductsByPlatformIds(List<String> platformProductIds, boolean forceUpdate);

    // ==================== 统一数据获取方法 ====================

    /**
     * 获取产品数据（优先TzProduct，不存在则自动同步）
     *
     * <p>
     * 这是ProductService应该使用的主要方法，取代直接调用PdcProductMappingRepository
     * </p>
     *
     * @param platformProductId 平台产品ID
     * @return 产品DTO，如果同步失败返回null
     */
    TzProductDTO getOrSyncProductByPlatformId(String platformProductId);

    /**
     * 获取原始阿里巴巴产品详情数据
     *
     * <p>
     * 当TzProduct数据不完整时的降级方案
     * </p>
     *
     * @param platformProductId 平台产品ID
     * @param forceRefresh      是否强制刷新缓存
     * @return 阿里巴巴产品详情DTO
     */
    AlibabaProductDetailDTO getAlibabaProductDetail(String platformProductId, boolean forceRefresh);

    // ==================== SKU相关方法 ====================

    /**
     * 根据SPU ID获取SKU列表
     *
     * @param spuId SPU ID
     * @return SKU列表
     */
    List<TzProductSku> getSkuListBySpuId(Long spuId);

    /**
     * 根据平台产品ID和平台SKU ID获取SKU
     *
     * @param platformProductId 平台产品ID
     * @param platformSkuId     平台SKU ID
     * @return SKU实体
     */
    TzProductSku getSkuByPlatformIds(String platformProductId, String platformSkuId);

    /**
     * 获取单品的默认SKU
     *
     * @param spuId SPU ID
     * @return 默认SKU，不存在返回null
     */
    TzProductSku getSingleItemDefaultSku(Long spuId);

    /**
     * 强制为单品创建默认SKU
     *
     * @param spuId             SPU ID
     * @param platformProductId 平台产品ID
     * @return 创建的默认SKU
     */
    TzProductSku forceCreateDefaultSkuForSingleItem(Long spuId, String platformProductId);

    // ==================== 价格相关 ====================

    /**
     * 检查产品是否为单品
     *
     * @param platformProductId 平台产品ID
     * @return true-单品，false-多规格商品
     */
    boolean isSingleItem(String platformProductId);

    /**
     * 获取单品价格
     *
     * @param platformProductId 平台产品ID
     * @return 单品价格
     */
    BigDecimal getSingleItemPrice(String platformProductId);

}
