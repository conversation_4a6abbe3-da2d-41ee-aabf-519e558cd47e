/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.support.alibaba.webhook.processor.impl;

import com.fulfillmen.shop.dao.mapper.TzOrderItemMapper;
import com.fulfillmen.shop.dao.mapper.TzOrderPurchaseMapper;
import com.fulfillmen.shop.dao.mapper.TzOrderSupplierMapper;
import com.fulfillmen.shop.domain.entity.TzOrderPurchase;
import com.fulfillmen.shop.domain.entity.TzOrderSupplier;
import com.fulfillmen.shop.domain.entity.enums.TzOrderItemStatusEnum;
import com.fulfillmen.shop.domain.entity.enums.TzOrderPurchaseStatusEnum;
import com.fulfillmen.shop.domain.entity.enums.TzOrderSupplierStatusEnum;
import com.fulfillmen.shop.manager.support.alibaba.webhook.handler.OrderContextRecord;
import com.fulfillmen.shop.manager.support.alibaba.webhook.processor.OrderEventProcessor;
import com.fulfillmen.shop.manager.support.wms.IWmsManager;
import com.fulfillmen.support.alibaba.api.response.order.OrderDetailResponse.OrderDetail;
import com.fulfillmen.support.alibaba.enums.OrderMessageTypeEnums;
import com.fulfillmen.support.wms.dto.enums.WmsOrderStatusEnum;
import com.fulfillmen.support.wms.dto.request.WmsPurchaseOrderDetailReq;
import com.fulfillmen.support.wms.dto.response.WmsPurchaseOrderDetailsRes;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionTemplate;

/**
 * 订单完成事件处理器
 * <p>
 * 策略实现类，专门处理 {@link OrderMessageTypeEnums#ORDER_BUYER_VIEW_ORDER_SUCCESS} 事件。
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/08/08
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OrderCompletionProcessor implements OrderEventProcessor<OrderMessageTypeEnums, OrderContextRecord> {

    private final TzOrderSupplierMapper tzOrderSupplierMapper;
    private final TzOrderPurchaseMapper tzOrderPurchaseMapper;
    private final TzOrderItemMapper tzOrderItemMapper;
    private final IWmsManager wmsManager;
    private final TransactionTemplate transactionTemplate;

    @Override
    public void process(OrderContextRecord context) {
        String orderId = context.getAlibabaOrderIdStr();
        log.info("开始处理订单完成事件: orderId={}", orderId);

        try {
            // 1. 更新本地订单状态为已完成
            updateLocalOrderStatusToCompleted(context);

            // 2. 通知WMS订单完成
            notifyWmsOrderCompleted(context);

            // 3. TODO 处理结算和统计数据更新
//            processOrderSettlement(context);

            log.info("订单完成事件处理完成: orderId={}", orderId);
        } catch (Exception e) {
            log.error("处理订单完成事件时发生异常: orderId={}, error={}", orderId, e.getMessage(), e);
            throw new RuntimeException("处理订单完成事件失败: orderId=" + orderId, e);
        }
    }

    /**
     * 更新本地数据库中的订单状态为"已完成"。
     */
    private void updateLocalOrderStatusToCompleted(OrderContextRecord context) {
        log.debug("更新本地订单状态为已完成: orderId={}", context.getAlibabaOrderIdStr());

        TzOrderSupplier orderSupplier = context.getTzOrderSupplierByOrderId(context.getAlibabaOrderIdStr());

        // 防御性检查：确保订单供应商存在
        if (orderSupplier == null) {
            log.error("未找到对应的供应商订单，无法更新状态: orderId={}", context.getAlibabaOrderIdStr());
            throw new IllegalStateException("供应商订单不存在: " + context.getAlibabaOrderIdStr());
        }

        // 幂等性检查：如果已经是已完成状态，跳过更新
        if (orderSupplier.getStatus() == TzOrderSupplierStatusEnum.COMPLETED) {
            log.warn("供应商订单 [{}] 状态已经为'已完成'，跳过数据库更新。", orderSupplier.getPlatformOrderId());
            return;
        }

        TzOrderPurchase purchase = context.tzOrderPurchase();

        transactionTemplate.executeWithoutResult(transactionStatus -> {
            try {
                // 1. 更新供应商订单状态为已完成
                orderSupplier.setStatus(TzOrderSupplierStatusEnum.COMPLETED);
                this.tzOrderSupplierMapper.updateById(orderSupplier);

                // 2. 更新该供应商订单下所有订单项的状态为已完成
                context.getOrderItemsBySupplierOrderId(orderSupplier.getId())
                    .forEach(item -> item.setStatus(TzOrderItemStatusEnum.COMPLETED));
                this.tzOrderItemMapper.updateBatchById(context.tzOrderItems());

                // 3. 检查是否所有供应商订单都已完成，如果是，则更新整个采购订单状态
                boolean allSuppliersCompleted = context.tzOrderSuppliers().stream()
                    .allMatch(s -> s.getId().equals(orderSupplier.getId())
                        || s.getStatus() == TzOrderSupplierStatusEnum.COMPLETED);

                if (allSuppliersCompleted && purchase.getOrderStatus() != TzOrderPurchaseStatusEnum.ORDER_COMPLETED) {
                    log.info("采购订单 [{}] 的所有供应商订单均已完成，将更新采购订单状态为已完成。", purchase.getPurchaseOrderNo());
                    purchase.setOrderStatus(TzOrderPurchaseStatusEnum.ORDER_COMPLETED);
                    this.tzOrderPurchaseMapper.updateById(purchase);
                }

            } catch (Exception e) {
                log.error("更新订单完成状态到数据库时失败: purchaseNo={}, error={}", purchase.getPurchaseOrderNo(), e.getMessage(), e);
                transactionStatus.setRollbackOnly();
                throw e;
            }
        });
    }

    /**
     * 通知WMS系统订单已完成。
     */
    private void notifyWmsOrderCompleted(OrderContextRecord context) {
        WmsPurchaseOrderDetailsRes wmsPurchaseOrderDetail = context.getWmsPurchaseOrderDetail();
        OrderDetail alibabaOrderDetail = context.alibabaOrderDetail();

        log.debug("通知WMS订单完成: wmsPurchaseNo={}", wmsPurchaseOrderDetail.getPurchaseNo());
        try {
            // 方案1: 通过updateWmsPurchaseOrder方法更新订单状态
            // 构建更新请求，标记订单为已完成状态
            // 这里可以调用 wmsManager.updateWmsPurchaseOrder() 方法
            if (Objects.equals(wmsPurchaseOrderDetail.getStatus(), WmsOrderStatusEnum.COMPLETED)) {
                log.info("WMS订单 : [purchaseNo: {} - status: {}] ，已经是完成的状态了。", wmsPurchaseOrderDetail.getPurchaseNo(), wmsPurchaseOrderDetail.getStatus());
                return;
            }
            // 更新 wms 订单状态
            this.wmsManager.updateWmsPurchaseOrder(WmsPurchaseOrderDetailReq.builder()
                .purchaseNo(wmsPurchaseOrderDetail.getPurchaseNo())
                .orderId(wmsPurchaseOrderDetail.getOrderId())
                .completeTime(Objects.isNull(alibabaOrderDetail.getBaseInfo().getCompleteTime()) ? LocalDateTime.now() : alibabaOrderDetail.getBaseInfo().getCompleteTime())
                .status(WmsOrderStatusEnum.COMPLETED)
                .build());

            // 暂时只记录日志，待WMS接口明确后再实现
            log.warn("WMS订单完成通知功能待实现: wmsPurchaseNo={}, status={}",
                wmsPurchaseOrderDetail.getPurchaseNo(), wmsPurchaseOrderDetail.getStatus());

            // 记录关键的WMS订单信息用于后续处理
            log.info("WMS订单当前状态: purchaseNo={}, cusCode={}, status={}, createTime={}",
                wmsPurchaseOrderDetail.getPurchaseNo(),
                wmsPurchaseOrderDetail.getCusCode(),
                wmsPurchaseOrderDetail.getStatus(),
                wmsPurchaseOrderDetail.getCreateTime());

        } catch (Exception e) {
            // 如果WMS通知失败，我们只记录日志，不中断主流程
            log.error("通知WMS订单完成失败: wmsPurchaseNo={}, error={}",
                wmsPurchaseOrderDetail.getPurchaseNo(), e.getMessage());
        }
    }

    @Override
    public List<OrderMessageTypeEnums> supports() {
        return List.of(OrderMessageTypeEnums.ORDER_BUYER_VIEW_ORDER_SUCCESS);
    }
}
