/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.support.wms.convert;

import cn.hutool.core.util.NumberUtil;
import com.fulfillmen.shop.common.context.OrderContextDTO;
import com.fulfillmen.shop.domain.entity.TzOrderItem;
import com.fulfillmen.shop.domain.entity.TzOrderPurchase;
import com.fulfillmen.shop.domain.entity.TzOrderSupplier;
import com.fulfillmen.shop.domain.entity.enums.OrderSupplierSyncStatusEnums;
import com.fulfillmen.shop.domain.entity.enums.PlatformCodeEnum;
import com.fulfillmen.shop.domain.entity.enums.TzOrderItemStatusEnum;
import com.fulfillmen.shop.domain.entity.enums.TzOrderPurchaseBuyerTypeEnums;
import com.fulfillmen.shop.domain.entity.enums.TzOrderPurchaseStatusEnum;
import com.fulfillmen.shop.domain.entity.enums.TzOrderSupplierStatusEnum;
import com.fulfillmen.support.wms.dto.enums.WmsOrderStatusEnum;
import com.fulfillmen.support.wms.dto.enums.WmsPayTypeEnum;
import com.fulfillmen.support.wms.dto.request.WmsPurchaseOrderDetailReq;
import com.fulfillmen.support.wms.dto.request.WmsPurchaseOrderDetailsReq;
import com.fulfillmen.support.wms.dto.request.WmsPurchaseOrderReq;
import com.fulfillmen.support.wms.dto.response.WmsPurchaseOrderDetailsItemRes;
import com.fulfillmen.support.wms.dto.response.WmsPurchaseOrderDetailsRes;
import com.google.common.collect.Lists;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import me.ahoo.cosid.IdGenerator;
import me.ahoo.cosid.provider.DefaultIdGeneratorProvider;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * 采购订单转换器
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface WmsPurchaseOrderConvert {

    WmsPurchaseOrderConvert INSTANCE = Mappers.getMapper(WmsPurchaseOrderConvert.class);

    /**
     * 默认租户ID
     */
    Long DEFAULT_TENANT_ID = 10000L;

    /**
     * 将供应商的采购单转换成 wms 采购订单请求
     *
     * <pre>
     * 创建供应商的采购单
     * </pre>
     *
     * @param purchaseOrder 采购单
     * @param supplierOrder 供应商采购单
     * @param orderItems    订单商品列表
     * @return WmsCreateOrderReq
     */
    default WmsPurchaseOrderReq initWmsCreateOrderRequest(TzOrderPurchase purchaseOrder,
      TzOrderSupplier supplierOrder, List<TzOrderItem> orderItems) {
        WmsOrderStatusEnum wmsOrderStatusEnum = Objects.equals(purchaseOrder.getOrderStatus(), TzOrderPurchaseStatusEnum.PAYMENT_PENDING) ? WmsOrderStatusEnum.PENDING_PAYMENT
          : WmsOrderStatusEnum.PAID_PENDING_REVIEW;
        return WmsPurchaseOrderReq.builder()
          // 采购单号
          .nayaPurchaseNo(purchaseOrder.getPurchaseOrderNo())
          // 供应商单号
          .shopOrderId(supplierOrder.getSupplierOrderNo())
          // 1688 订单 id
          .orderId(supplierOrder.getPlatformOrderId() != null ? Long.parseLong(supplierOrder.getPlatformOrderId()) : 0L)
          // 卖家 id
          .sellerOpenId(supplierOrder.getSupplierId())
          // 采购订单状态
          .status(wmsOrderStatusEnum)
          // 必填 通过仓库 ID 获取，默认值 536 ，代表 惠州仓
          // TODO 2025年07月17日10:35:41 后期需要改掉
          .storeId("536")
          // 当前的用户 id
          .createUser(String.valueOf(purchaseOrder.getBuyerId()))
          .createTime(supplierOrder.getGmtCreated())
          .platform(supplierOrder.getPlatformCode().getCode())
          // 必填
          .payType(WmsPayTypeEnum.BALANCE)
          // 1688 外部交易流水号
          .outTradeNo(supplierOrder.getPlatformTradeNo())
          // 物流单号
          .trackingNo(supplierOrder.getPlatformTrackingNo())
          // 支付的链接
          .payUrl(supplierOrder.getPlatformPayUrl())
          // 是否询价单
          .isRequestQuote(false)
          // 支付时间
          .paymentTime(supplierOrder.getPaymentDate() != null
            ? supplierOrder.getPaymentDate()
            : LocalDateTime.of(LocalDate.of(1970, 1, 1), LocalTime.MIN))
          // 发货时间
          .shippingTime(supplierOrder.getShippedDate() != null
            ? supplierOrder.getShippedDate()
            : LocalDateTime.of(LocalDate.of(1970, 1, 1), LocalTime.MIN))
          // 完成时间
          .completeTime(supplierOrder.getCompletedDate() != null
            ? supplierOrder.getCompletedDate()
            : LocalDateTime.of(LocalDate.of(1970, 1, 1), LocalTime.MIN))
          // 商品链接
          .link(null)
          // 服务费
          .serviceFee(supplierOrder.getServiceFee())
          // 商品金额 + 运费
          .total(supplierOrder.getCustomerGoodsAmount().add(supplierOrder.getCustomerFreightAmount()))
          // 运费
          .shippingFee(supplierOrder.getCustomerFreightAmount())
          // 原始总价
          .originalTotalPrice(NumberUtil.nullToZero(supplierOrder.getPayableAmountTotal()).compareTo(BigDecimal.ZERO) == 0
            ? supplierOrder.getCustomerTotalAmount() : supplierOrder.getPayableAmountTotal())
          // 原始运费 如果为 0 获取客户运费
          .originalShippingFee(supplierOrder.getPayableFreightAmount())
          .finalShoppingFee(supplierOrder.getPayableFreightAmount())
          // 商品原始总金额
          .productOriginalTotalAmount(BigDecimal.ZERO.compareTo(NumberUtil.nullToZero(supplierOrder.getPayableGoodsAmount())) == 0
            ? supplierOrder.getCustomerGoodsAmount() : supplierOrder.getPayableGoodsAmount())
          // 商品销售总金额
          .salesTotalAmount(supplierOrder.getCustomerGoodsAmount())
          // 折扣优惠
          .discount(supplierOrder.getPayableDiscountAmount())
          // plus 折扣
          .plusDiscount(supplierOrder.getPayablePlusDiscountAmount())
          // 优惠券折扣
          .couponDiscount(supplierOrder.getPayableCouponAmount())
          // 总金额
          .totalAmount(supplierOrder.getCustomerTotalAmount())
          // 商品最终总金额
          .productFinalTotalAmount(BigDecimal.ZERO.compareTo(NumberUtil.nullToZero(supplierOrder.getPayableGoodsAmount())) == 0
            ? supplierOrder.getCustomerGoodsAmount() : supplierOrder.getPayableGoodsAmount())
          // 最终运费
          .finalShoppingFee(BigDecimal.ZERO.compareTo(NumberUtil.nullToZero(supplierOrder.getPayableFreightAmount())) == 0
            ? supplierOrder.getCustomerFreightAmount() : supplierOrder.getPayableFreightAmount())
          // 平台状态
          .platformStatus(supplierOrder.getStatus().name())
          // 平台备注
          .platformRemark(supplierOrder.getStatus().getDescription())
          // 备注
          .remark(supplierOrder.getSupplierNotes())
          // 订单详情的列表
          .orderDetails(toWmsPurchaseOrderDetailsRes(orderItems))
          .build();
    }

    /**
     * 将TzOrderItem转换为WmsPurchaseOrderDetailsRes
     *
     * @param orderItems 订单商品列表
     * @return 转换成 wms 采购订单详情列表
     */
    default List<WmsPurchaseOrderDetailsReq> toWmsPurchaseOrderDetailsRes(List<TzOrderItem> orderItems) {
        List<WmsPurchaseOrderDetailsReq> wmsPurchaseOrderDetailsResList = Lists
          .newArrayListWithCapacity(orderItems.size());
        orderItems.forEach(item -> {
            // 商品规格
            String attrs = item.getSkuSpecs().stream().map(attr -> attr.getAttrKey() + ":" + attr.getAttrValue())
              .reduce((a, b) -> a + "," + b).orElse("");
            // 商品规格英文
            String attrsEn = item.getSkuSpecs().stream().map(attr -> attr.getAttrKeyTrans() + ":"
              + attr.getAttrValueTrans()).reduce((a, b) -> a + "," + b).orElse("");
            // 构建采购订单详情
            wmsPurchaseOrderDetailsResList.add(WmsPurchaseOrderDetailsReq.builder()
              .cnName(item.getProductTitle())
              .enName(item.getProductTitleEn())
              .skuId(item.getProductSkuId().toString())
              .productId(item.getProductSpuId().toString())
              .quantity(item.getQuantity().intValue())
              .imageUrl(item.getProductImageUrl())
              .unitPrice(item.getPrice())
              .skuAttrib(attrs)
              .skuAttribEn(attrsEn)
              .variantId(item.getPlatformSpecId())
              .originUnitPrice(item.getPrice())
              .finalUnitPrice(item.getPrice())
              .subTotal(item.getTotalAmount())
              .originSubTotalAmount(item.getTotalAmount())
              .finalSubTotalAmount(item.getTotalAmount())
              .weight(BigDecimal.ZERO)
              .build());
        });
        return wmsPurchaseOrderDetailsResList;
    }

    /**
     * 将供应商的采购单转换成 wms 采购订单详情请求
     *
     * @param purchaseOrder 采购单
     * @param supplierOrder 供应商采购单
     * @param orderItems    订单商品列表
     * @return WmsPurchaseOrderDetailReq
     */
    default WmsPurchaseOrderDetailReq toWmsPurchaseOrderDetailReq(TzOrderPurchase purchaseOrder, TzOrderSupplier supplierOrder, List<TzOrderItem> orderItems) {
        if (!Objects.equals(supplierOrder.getWmsSyncStatus(), OrderSupplierSyncStatusEnums.SYNCED) || !StringUtils.hasText(supplierOrder.getWmsPurchaseOrderNo())) {
            return null;
        }
        return WmsPurchaseOrderDetailReq.builder()
          // 采购单号
          .purchaseNo(supplierOrder.getWmsPurchaseOrderNo())
          // 供应商单号
          .shopOrderId(supplierOrder.getSupplierOrderNo())
          // 1688 订单 id
          .orderId(supplierOrder.getPlatformOrderId() != null ? Long.parseLong(supplierOrder.getPlatformOrderId()) : 0L)
          // 卖家 id
          .sellerOpenId(supplierOrder.getSupplierId())
          // 采购订单状态
          .status(convertPurchaseStatusToWmsStatus(purchaseOrder.getOrderStatus()))
          // 必填 通过仓库 ID 获取，默认值 536 ，代表 惠州仓
          // TODO 2025年07月17日10:35:41 后期需要改掉
          .storeId("536")
          // 当前的用户 id
          .createUser(String.valueOf(purchaseOrder.getBuyerId()))
          .createTime(supplierOrder.getGmtCreated())
          .platform(supplierOrder.getPlatformCode().name())
          // 必填
          .payType(WmsPayTypeEnum.BALANCE)
          // 1688 外部交易流水号
          .outTradeNo(supplierOrder.getPlatformTradeNo())
          // 支付的链接
          .payUrl(supplierOrder.getPlatformPayUrl())
          // 是否询价单
          .isRequestQuote(false)
          // 支付时间
          .paymentTime(supplierOrder.getPaymentDate() != null
            ? supplierOrder.getPaymentDate()
            : LocalDateTime.of(LocalDate.of(1970, 1, 1), LocalTime.MIN))
          // 发货时间
          .shippingTime(supplierOrder.getShippedDate() != null
            ? supplierOrder.getShippedDate()
            : LocalDateTime.of(LocalDate.of(1970, 1, 1), LocalTime.MIN))
          // 完成时间
          .completeTime(supplierOrder.getCompletedDate() != null
            ? supplierOrder.getCompletedDate()
            : LocalDateTime.of(LocalDate.of(1970, 1, 1), LocalTime.MIN))
          // 商品链接
          .link(null)
          // 物流单号
          .trackingNo(supplierOrder.getPlatformTrackingNo())
          // 服务费
          .serviceFee(supplierOrder.getServiceFee())
          // 商品金额 + 运费
          .total(supplierOrder.getCustomerGoodsAmount().add(supplierOrder.getCustomerFreightAmount()))
          // 运费
          .shippingFee(supplierOrder.getCustomerFreightAmount())
          // 原始总价
          .originalTotalPrice(supplierOrder.getPayableAmountTotal())
          // 原始运费
          .originalShippingFee(supplierOrder.getPayableFreightAmount())
          // 商品原始总金额
          .productOriginalTotalAmount(supplierOrder.getPayableGoodsAmount())
          // 商品销售总金额
          .productSalesTotalAmount(supplierOrder.getCustomerGoodsAmount())
          // 折扣优惠
          .discount(supplierOrder.getPayableDiscountAmount())
          // plus 折扣
          .plusDiscount(supplierOrder.getPayablePlusDiscountAmount())
          // 优惠券折扣
          .couponDiscount(supplierOrder.getPayableCouponAmount())
          // 总金额
          .totalAmount(supplierOrder.getCustomerTotalAmount())
          // 商品最终总金额
          .productFinalTotalAmount(BigDecimal.ZERO)
          // 最终运费
          .finalShoppingFee(supplierOrder.getPayableFreightAmount())
          // 平台状态
          .platformStatus(purchaseOrder.getOrderStatus().name())
          // 平台备注
          .platformRemark(supplierOrder.getStatus().getDescription())
          // 备注
          .remark(supplierOrder.getSupplierNotes())
          // 订单详情的列表
          .orderDetails(toWmsPurchaseOrderDetailsRes(orderItems))
          .build();
    }

    /**
     * 根据 WMS 采购单详情转换成系统的采购单上下文。并创建对应的 采购订单信息
     *
     * @param wmsPurchaseOrderDetailsRes WMS采购订单详情响应
     * @return 订单上下文
     */
    default OrderContextDTO initOrderContext(WmsPurchaseOrderDetailsRes wmsPurchaseOrderDetailsRes) {
        if (wmsPurchaseOrderDetailsRes == null) {
            return null;
        }

        try {
            // 创建采购订单
            TzOrderPurchase purchaseOrder = createTzOrderPurchaseFromWmsDetails(wmsPurchaseOrderDetailsRes);

            // 创建供应商订单 ，默认只有一个供应商订单。 wms 里一个采购单代表一个供应商订单 和 多个 采购单详情 - 商品信息
            List<TzOrderSupplier> supplierOrders = createTzOrderSuppliersFromWmsDetails(purchaseOrder.getId(), wmsPurchaseOrderDetailsRes);

            // 创建订单项
            List<TzOrderItem> orderItems = createTzOrderItemsFromWmsDetails(purchaseOrder.getId(),
              supplierOrders.isEmpty() ? null : supplierOrders.getFirst().getId(), wmsPurchaseOrderDetailsRes);

            return OrderContextDTO.builder()
              .purchaseOrder(purchaseOrder)
              .supplierOrders(supplierOrders)
              .orderItems(orderItems)
              .shoppingCartIds(Collections.emptyList())
              .build();

        } catch (Exception e) {
//            log.error("转换WMS采购订单详情失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 从 WmsPurchaseOrderDetailsRes 创建 TzOrderPurchase
     */
    default TzOrderPurchase createTzOrderPurchaseFromWmsDetails(WmsPurchaseOrderDetailsRes wmsOrderDetails) {
        IdGenerator idGenerator = DefaultIdGeneratorProvider.INSTANCE.getRequired("safe-js");

        return TzOrderPurchase.builder()
          .id(idGenerator.generate())
          .purchaseOrderNo(StringUtils.hasText(wmsOrderDetails.getNayaPurchaseNo()) ? wmsOrderDetails.getNayaPurchaseNo() : wmsOrderDetails.getPurchaseNo())
          // WMS订单默认买家ID为0
          .buyerId(Long.valueOf(wmsOrderDetails.getCusCode()))
          .buyerType(TzOrderPurchaseBuyerTypeEnums.WMS)
          .orderStatus(convertWmsStatusToPurchaseStatus(wmsOrderDetails.getStatus()))
          .orderDate(wmsOrderDetails.getCreateTime() != null ? wmsOrderDetails.getCreateTime() : LocalDateTime.now())
          // ------------------------ 客户付款信息 ------------------------
          .customerGoodsAmount(wmsOrderDetails.getProductSalesTotalAmount() != null ? wmsOrderDetails.getProductSalesTotalAmount() : BigDecimal.ZERO)
          .customerTotalFreight(wmsOrderDetails.getShippingFee() != null ? wmsOrderDetails.getShippingFee() : BigDecimal.ZERO)
          .customerTotalAmount(wmsOrderDetails.getTotalAmount() != null ? wmsOrderDetails.getTotalAmount() : BigDecimal.ZERO)
          .serviceFee(wmsOrderDetails.getServiceFee() != null ? wmsOrderDetails.getServiceFee() : BigDecimal.ZERO)
          // 仓库 Id
          .recipientWarehouseId(Long.valueOf(wmsOrderDetails.getStoreId()))
          // 支付时间
          .paidDate(wmsOrderDetails.getPaymentTime())
          // 订单完成时间
          .orderCompletedDate(wmsOrderDetails.getCompleteTime())
          // 当前交易的费率，默认 0 .等于 0 的时候代表订单的汇率是实时汇率
          .exchangeRateSnapshot(BigDecimal.ZERO)
          // ------------------------ 应付信息 ------------------------
          // 订单折扣
          .payableDiscountAmount(wmsOrderDetails.getDiscount() != null ? wmsOrderDetails.getDiscount() : BigDecimal.ZERO)
          // 会员折扣
          .payablePlusDiscountAmount(wmsOrderDetails.getPlusDiscount() != null ? wmsOrderDetails.getPlusDiscount() : BigDecimal.ZERO)
          // 红包/店铺优惠
          .payableCouponAmount(wmsOrderDetails.getCouponDiscount() != null ? wmsOrderDetails.getCouponDiscount() : BigDecimal.ZERO)
          // 运费
          .payableFreightTotal(wmsOrderDetails.getOriginalShippingFee() != null ? wmsOrderDetails.getOriginalShippingFee() : BigDecimal.ZERO)
          // 商品总成本
          .payableGoodsAmount(wmsOrderDetails.getProductFinalTotalAmount() != null ? wmsOrderDetails.getProductFinalTotalAmount() : BigDecimal.ZERO)
          // 应付总价
          .payableAmountTotal(wmsOrderDetails.getAlibabaTotalAmount() != null ? wmsOrderDetails.getAlibabaTotalAmount() : BigDecimal.ZERO)
          // ------------------------ 实付信息 ------------------------
          // 实付总金额
          .actualPaymentAmount(wmsOrderDetails.getProductFinalTotalAmount() != null ? wmsOrderDetails.getProductFinalTotalAmount() : BigDecimal.ZERO)
          // 实付总产品
          .actualPaymentGoodsAmount(wmsOrderDetails.getProductFinalTotalAmount() != null ? wmsOrderDetails.getProductFinalTotalAmount() : BigDecimal.ZERO)
          // 实付运费
          .actualPaymentFreightAmount(wmsOrderDetails.getFinalShoppingFee() != null ? wmsOrderDetails.getFinalShoppingFee() : BigDecimal.ZERO)
          // 实付折扣
          .actualPaymentDiscountAmount(wmsOrderDetails.getDiscount() != null ? wmsOrderDetails.getDiscount() : BigDecimal.ZERO)
          // 实付红包/店铺优惠
          .actualPaymentCouponAmount(wmsOrderDetails.getCouponDiscount() != null ? wmsOrderDetails.getCouponDiscount() : BigDecimal.ZERO)
          // 实付Plus会员折扣
          .actualPaymentPlusAmount(wmsOrderDetails.getPlusDiscount() != null ? wmsOrderDetails.getPlusDiscount() : BigDecimal.ZERO)
          // 默认租户ID
          .tenantId(DEFAULT_TENANT_ID)
          .gmtCreated(wmsOrderDetails.getCreateTime())
          .gmtModified(wmsOrderDetails.getCompleteTime() == null ? LocalDateTime.now() : wmsOrderDetails.getCompleteTime())
          .build();
    }

    /**
     * 从 WmsPurchaseOrderDetailsRes 创建 TzOrderSupplier 列表
     */
    default List<TzOrderSupplier> createTzOrderSuppliersFromWmsDetails(Long purchaseOrderId, WmsPurchaseOrderDetailsRes wmsOrderDetails) {
        IdGenerator idGenerator = DefaultIdGeneratorProvider.INSTANCE.getRequired("safe-js");

        TzOrderSupplier supplierOrder = TzOrderSupplier.builder()
          .id(idGenerator.generate())
          .purchaseOrderId(purchaseOrderId)
          .supplierOrderNo(wmsOrderDetails.getShopOrderId())
          .platformCode(PlatformCodeEnum.PLATFORM_CODE_1688)
          .supplierId(wmsOrderDetails.getSellerOpenId() != null ? wmsOrderDetails.getSellerOpenId() : "unknown")
//          .supplierName(wmsOrderDetails)
          .externalSyncStatus(OrderSupplierSyncStatusEnums.SYNCED)
          .platformOrderId(wmsOrderDetails.getOrderId() != null ? wmsOrderDetails.getOrderId().toString() : null)
          .platformTradeNo(wmsOrderDetails.getOutTradeNo())
          .platformTrackingNo(wmsOrderDetails.getTrackingNo())
          .platformPayUrl(wmsOrderDetails.getPayUrl())
          .status(convertWmsStatusToSupplierStatus(wmsOrderDetails.getStatus()))
          .orderDate(wmsOrderDetails.getCreateTime() != null ? wmsOrderDetails.getCreateTime() : LocalDateTime.now())
          .paymentDate(wmsOrderDetails.getPaymentTime())
          .shippedDate(wmsOrderDetails.getShippingTime())
          .completedDate(wmsOrderDetails.getCompleteTime())
          // ------------------------ 客户付款信息 ------------------------
          .customerGoodsAmount(wmsOrderDetails.getProductSalesTotalAmount() != null ? wmsOrderDetails.getProductSalesTotalAmount() : BigDecimal.ZERO)
          .customerFreightAmount(wmsOrderDetails.getShippingFee() != null ? wmsOrderDetails.getShippingFee() : BigDecimal.ZERO)
          .customerTotalAmount(wmsOrderDetails.getTotalAmount() != null ? wmsOrderDetails.getTotalAmount() : BigDecimal.ZERO)
          .serviceFee(wmsOrderDetails.getServiceFee() != null ? wmsOrderDetails.getServiceFee() : BigDecimal.ZERO)
          // ------------------------ 应付信息 ------------------------
          // 订单折扣
          .payableDiscountAmount(wmsOrderDetails.getDiscount() != null ? wmsOrderDetails.getDiscount() : BigDecimal.ZERO)
          // 会员折扣
          .payablePlusDiscountAmount(wmsOrderDetails.getPlusDiscount() != null ? wmsOrderDetails.getPlusDiscount() : BigDecimal.ZERO)
          // 红包/店铺优惠
          .payableCouponAmount(wmsOrderDetails.getCouponDiscount() != null ? wmsOrderDetails.getCouponDiscount() : BigDecimal.ZERO)
          // 运费
          .payableFreightAmount(wmsOrderDetails.getOriginalShippingFee() != null ? wmsOrderDetails.getOriginalShippingFee() : BigDecimal.ZERO)
          // 商品总成本
          .payableGoodsAmount(wmsOrderDetails.getProductFinalTotalAmount() != null ? wmsOrderDetails.getProductFinalTotalAmount() : BigDecimal.ZERO)
          // 应付总价
          .payableAmountTotal(wmsOrderDetails.getAlibabaTotalAmount() != null ? wmsOrderDetails.getAlibabaTotalAmount() : BigDecimal.ZERO)
          // ------------------------ 实付信息 ------------------------
          // 实付总金额
          .actualPaymentAmount(wmsOrderDetails.getProductFinalTotalAmount() != null ? wmsOrderDetails.getProductFinalTotalAmount() : BigDecimal.ZERO)
          // 实付总产品
          .actualPaymentGoodsAmount(wmsOrderDetails.getProductFinalTotalAmount() != null ? wmsOrderDetails.getProductFinalTotalAmount() : BigDecimal.ZERO)
          // 实付运费
          .actualPaymentFreightAmount(wmsOrderDetails.getFinalShoppingFee() != null ? wmsOrderDetails.getFinalShoppingFee() : BigDecimal.ZERO)
          // 实付折扣
          .actualPaymentDiscountAmount(wmsOrderDetails.getDiscount() != null ? wmsOrderDetails.getDiscount() : BigDecimal.ZERO)
          // 实付红包/店铺优惠
          .actualPaymentCouponAmount(wmsOrderDetails.getCouponDiscount() != null ? wmsOrderDetails.getCouponDiscount() : BigDecimal.ZERO)
          // 实付Plus会员折扣
          .actualPaymentPlusAmount(wmsOrderDetails.getPlusDiscount() != null ? wmsOrderDetails.getPlusDiscount() : BigDecimal.ZERO)
          // 默认租户ID
          .tenantId(DEFAULT_TENANT_ID)
          .gmtCreated(wmsOrderDetails.getCreateTime())
          .gmtModified(wmsOrderDetails.getCompleteTime() == null ? LocalDateTime.now() : wmsOrderDetails.getCompleteTime())
          .build();

        return Collections.singletonList(supplierOrder);
    }

    /**
     * 从 WmsPurchaseOrderDetailsRes 创建 TzOrderItem 列表
     */
    default List<TzOrderItem> createTzOrderItemsFromWmsDetails(Long purchaseOrderId, Long supplierOrderId,
      WmsPurchaseOrderDetailsRes wmsOrderDetails) {

        if (CollectionUtils.isEmpty(wmsOrderDetails.getOrderDetailsItemRes())) {
//            log.warn("WMS订单详情中没有商品信息: purchaseNo={}", wmsOrderDetails.getPurchaseNo());
            return Collections.emptyList();
        }

        IdGenerator idGenerator = DefaultIdGeneratorProvider.INSTANCE.getRequired("safe-js");
        List<TzOrderItem> orderItems = new ArrayList<>();

        int lineNumber = 1;
        for (WmsPurchaseOrderDetailsItemRes itemRes : wmsOrderDetails.getOrderDetailsItemRes()) {
            TzOrderItem orderItem = TzOrderItem.builder()
              .id(idGenerator.generate())
              .purchaseOrderId(purchaseOrderId)
              .supplierOrderId(supplierOrderId)
              .platformOrderId(wmsOrderDetails.getOrderId() != null ? wmsOrderDetails.getOrderId().toString() : null)
              .lineNumber(lineNumber++)
              .platformProductId(itemRes.getProductId())
              .platformSkuId(itemRes.getSkuId())
              .platformSpecId(itemRes.getVariantId())
              .productTitle(itemRes.getCnName())
              .productTitleEn(itemRes.getEnName())
              .productImageUrl(itemRes.getImageUrl())
              .price(itemRes.getUnitPrice() != null ? itemRes.getUnitPrice() : BigDecimal.ZERO)
              .quantity(itemRes.getQuantity() != null ? BigDecimal.valueOf(itemRes.getQuantity()) : BigDecimal.ONE)
              .totalAmount(itemRes.getSubTotal() != null ? itemRes.getSubTotal() : BigDecimal.ZERO)
              .actualPaymentAmount(itemRes.getFinalSubTotalAmount() != null ? itemRes.getFinalSubTotalAmount() : BigDecimal.ZERO)
              .status(TzOrderItemStatusEnum.PENDING)
              .tenantId(DEFAULT_TENANT_ID)
              .gmtCreated(LocalDateTime.now())
              .gmtModified(LocalDateTime.now())
              .build();

            orderItems.add(orderItem);
        }
        return orderItems;
    }

    /**
     * 将 WMS 订单状态转换为采购订单状态
     */
    default TzOrderPurchaseStatusEnum convertWmsStatusToPurchaseStatus(WmsOrderStatusEnum wmsStatus) {
        if (wmsStatus == null) {
            return TzOrderPurchaseStatusEnum.PAYMENT_PENDING;
        }

        return switch (wmsStatus) {
            case PAID_PENDING_REVIEW -> TzOrderPurchaseStatusEnum.PAYMENT_COMPLETED;
            case REVIEWED_PENDING_PURCHASE -> TzOrderPurchaseStatusEnum.PENDING_REVIEW;
            case PURCHASED_PENDING_SHIPMENT -> TzOrderPurchaseStatusEnum.PROCUREMENT_COMPLETED;
            case SHIPPED_PENDING_RECEIPT -> TzOrderPurchaseStatusEnum.SUPPLIER_SHIPPED;
            case SIGNED -> TzOrderPurchaseStatusEnum.WAREHOUSE_RECEIVED;
            case COMPLETED -> TzOrderPurchaseStatusEnum.ORDER_COMPLETED;
            case CANCELED -> TzOrderPurchaseStatusEnum.ORDER_CANCELLED;
            default -> TzOrderPurchaseStatusEnum.PAYMENT_PENDING;
        };
    }

    /**
     * 将 WMS 订单状态转换为采购订单状态
     */
    default WmsOrderStatusEnum convertPurchaseStatusToWmsStatus(TzOrderPurchaseStatusEnum tzOrderPurchaseStatusEnum) {
        if (tzOrderPurchaseStatusEnum == null) {
            return WmsOrderStatusEnum.PENDING_PAYMENT;
        }

        return switch (tzOrderPurchaseStatusEnum) {
            case PAYMENT_COMPLETED -> WmsOrderStatusEnum.PAID_PENDING_REVIEW;
            case PROCUREMENT_IN_PROGRESS -> WmsOrderStatusEnum.REVIEWED_PENDING_PURCHASE;
            case PROCUREMENT_COMPLETED -> WmsOrderStatusEnum.PURCHASED_PENDING_SHIPMENT;
            case SUPPLIER_SHIPPED -> WmsOrderStatusEnum.SHIPPED_PENDING_RECEIPT;
            case WAREHOUSE_RECEIVED -> WmsOrderStatusEnum.SIGNED;
            case ORDER_COMPLETED -> WmsOrderStatusEnum.COMPLETED;
            case ORDER_CANCELLED -> WmsOrderStatusEnum.CANCELED;
            default -> WmsOrderStatusEnum.PENDING_PAYMENT;
        };
    }

    /**
     * 将 WMS 订单状态转换为供应商订单状态
     */
    default TzOrderSupplierStatusEnum convertWmsStatusToSupplierStatus(WmsOrderStatusEnum wmsStatus) {
        if (wmsStatus == null) {
            return TzOrderSupplierStatusEnum.PENDING_PAYMENT;
        }

        return switch (wmsStatus) {
            // 待付款
            case PENDING_PAYMENT -> TzOrderSupplierStatusEnum.PENDING_PAYMENT;
            // 待发货
            case PURCHASED_PENDING_SHIPMENT -> TzOrderSupplierStatusEnum.PENDING_SHIPMENT;
            // 待收货
            case SHIPPED_PENDING_RECEIPT -> TzOrderSupplierStatusEnum.SHIPPED;
            // 签收
            case SIGNED -> TzOrderSupplierStatusEnum.WAREHOUSE_RECEIVED;
            // 完成
            case COMPLETED -> TzOrderSupplierStatusEnum.COMPLETED;
            // 取消
            case CANCELED -> TzOrderSupplierStatusEnum.CANCELLED;
            default -> TzOrderSupplierStatusEnum.PENDING_PAYMENT;
        };
    }
}
