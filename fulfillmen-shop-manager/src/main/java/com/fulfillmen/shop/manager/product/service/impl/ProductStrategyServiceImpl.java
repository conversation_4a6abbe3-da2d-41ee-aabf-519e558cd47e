/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.product.service.impl;

import com.fulfillmen.shop.common.context.UserContextHolder;
import com.fulfillmen.shop.common.tenant.EnhancedTenantContextHolder;
import com.fulfillmen.shop.domain.dto.TzProductDTO;
import com.fulfillmen.shop.manager.product.chain.ProductProcessor;
import com.fulfillmen.shop.manager.product.chain.ProductProcessorChain;
import com.fulfillmen.shop.manager.product.context.ProductProcessingContext;
import com.fulfillmen.shop.manager.product.context.TenantContext;
import com.fulfillmen.shop.manager.product.context.UserPricingContext;
import com.fulfillmen.shop.manager.product.service.IProductStrategyService;
import com.fulfillmen.shop.manager.product.strategy.ProductStrategy;
import com.fulfillmen.shop.manager.product.strategy.ProductStrategyFactory;
import com.fulfillmen.shop.manager.product.strategy.pricing.PricingStrategy;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 产品策略服务实现
 *
 * <pre>
 * 核心功能：
 * 1. 整合责任链处理器和策略工厂
 * 2. 提供产品策略处理的统一入口
 * 3. 管理处理上下文的构建和生命周期
 * 4. 提供系统监控和统计功能
 *
 * 处理流程：
 * 1. 构建处理上下文（用户、租户、产品信息）
 * 2. 执行责任链处理（定价、库存、图片等）
 * 3. 应用策略结果并验证数据完整性
 * 4. 记录处理统计和性能指标
 *
 * 设计特点：
 * - 模块化设计：策略和处理器解耦
 * - 可扩展性：易于添加新的策略和处理器
 * - 高性能：支持缓存和并行处理
 * - 可监控：提供详细的执行统计
 * </pre>
 *
 * <AUTHOR>
 * @date 2025/08/22
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ProductStrategyServiceImpl implements IProductStrategyService {

    private final ProductProcessorChain processorChain;
    private final ProductStrategyFactory strategyFactory;

    // 执行统计
    private final AtomicInteger totalProcessCount = new AtomicInteger(0);
    private final AtomicInteger successProcessCount = new AtomicInteger(0);
    private final AtomicInteger failureProcessCount = new AtomicInteger(0);

    @Override
    public TzProductDTO processProduct(TzProductDTO product, ProductProcessingContext context) {
        if (product == null) {
            log.warn("产品策略服务: 产品数据为空");
            return null;
        }

        if (context == null) {
            log.warn("产品策略服务: 处理上下文为空");
            return product;
        }

        try {
            // 记录处理开始
            totalProcessCount.incrementAndGet();
            long startTime = System.currentTimeMillis();

            log.debug("开始处理产品策略: 产品ID={}, 用户ID={}, 租户ID={}",
              context.getProductId(), context.getUserId(), context.getTenantId());

            // 将产品数据放入上下文
            context.setAttribute("product", product);
            context.setAttribute("originalProduct", cloneProduct(product));

            // 执行责任链处理
            ProductProcessor.ProcessResult result = processorChain.processProduct(context);

            // 获取处理后的产品数据
            TzProductDTO processedProduct = context.getAttribute("product");
            if (processedProduct == null) {
                log.warn("处理后的产品数据为空，使用原始产品数据");
                processedProduct = product;
            }

            // 记录处理结果
            long duration = System.currentTimeMillis() - startTime;
            if (result.isSuccess()) {
                successProcessCount.incrementAndGet();
                log.debug("产品策略处理成功: 产品ID={}, 耗时={}ms", context.getProductId(), duration);
            } else {
                failureProcessCount.incrementAndGet();
                log.warn("产品策略处理失败: 产品ID={}, 原因={}", context.getProductId(), result.getMessage());
            }

            // 记录性能信息
            context.setAttribute("processingDuration", duration);
            context.setAttribute("processingSuccess", result.isSuccess());

            return processedProduct;

        } catch (Exception e) {
            failureProcessCount.incrementAndGet();
            log.error("产品策略处理异常: 产品ID={}", context.getProductId(), e);
            // 异常时返回原始产品
            return product;
        }
    }

    @Override
    public TzProductDTO processProduct(TzProductDTO product, Long userId, String tenantId, String platformProductId) {
        // 构建处理上下文
        ProductProcessingContext context = buildProcessingContext(userId, tenantId, platformProductId, product.getId());

        // 执行完整处理流程
        return processProduct(product, context);
    }

    @Override
    public List<TzProductDTO> batchProcessProduct(List<TzProductDTO> products) {
        if (products == null || products.isEmpty()) {
            return products;
        }

        // 获取智能定价策略
        PricingStrategy pricingStrategy = strategyFactory.getSmartPricingStrategy();
        if (pricingStrategy == null) {
            log.warn("未找到适用的定价策略，跳过批量定价");
            return products;
        }

        log.info("开始批量定价: 策略={}, 产品数量={}", pricingStrategy.getStrategyName(), products.size());

        int successCount = 0;
        for (TzProductDTO product : products) {
            try {
                ProductProcessingContext context = ProductProcessingContext.builder()
                  .productId(product.getId())
                  .platformProductId(product.getPdcPlatformProductId())
                  .userId(UserContextHolder.getUserId())
                  .tenantId(EnhancedTenantContextHolder.getCurrentTenantId())
                  .build();

                TzProductDTO processedProduct = pricingStrategy.process(product, context);
                if (processedProduct != null) {
                    successCount++;
                }
            } catch (Exception e) {
                log.error("批量定价异常: 产品ID={}", product.getId(), e);
            }
        }

        log.info("批量定价完成: 成功={}/{}", successCount, products.size());

        return products;
    }

    @Override
    public TzProductDTO applyPricingStrategy(TzProductDTO product, UserPricingContext userContext) {
        if (product == null || userContext == null) {
            log.warn("定价策略应用: 产品数据或用户上下文为空");
            return product;
        }

        try {
            // 构建简化的处理上下文
            ProductProcessingContext context = ProductProcessingContext.builder()
              .userId(userContext.getUserId())
              .userPricingContext(userContext)
              .productId(product.getId())
              .build();

            // 获取最佳定价策略
            PricingStrategy pricingStrategy = strategyFactory.getBestPricingStrategy(context);
            if (pricingStrategy == null) {
                log.debug("没有找到适用的定价策略，返回原始产品");
                return product;
            }

            // 应用定价策略
            TzProductDTO processedProduct = pricingStrategy.process(product, context);

            log.debug("定价策略应用完成: 策略={}, 用户ID={}",
              pricingStrategy.getStrategyName(), userContext.getUserId());

            return processedProduct != null ? processedProduct : product;

        } catch (Exception e) {
            log.error("定价策略应用异常: 用户ID={}", userContext.getUserId(), e);
            return product;
        }
    }

    @Override
    public TzProductDTO applyPricingStrategy(TzProductDTO product, UserPricingContext userContext, TenantContext tenantContext) {
        if (product == null || userContext == null || tenantContext == null) {
            log.warn("定价策略应用: 产品数据、用户上下文或租户上下文为空");
            return product;
        }

        try {
            // 构建包含租户信息的处理上下文
            ProductProcessingContext context = ProductProcessingContext.builder()
              .userId(userContext.getUserId())
              .tenantId(tenantContext.getTenantId())
              .userPricingContext(userContext)
              .tenantContext(tenantContext)
              .productId(product.getId())
              .build();

            // 获取最佳定价策略（可能会选择租户策略）
            PricingStrategy pricingStrategy = strategyFactory.getBestPricingStrategy(context);
            if (pricingStrategy == null) {
                log.debug("没有找到适用的定价策略，返回原始产品");
                return product;
            }

            // 应用定价策略
            TzProductDTO processedProduct = pricingStrategy.process(product, context);

            log.debug("租户定价策略应用完成: 策略={}, 用户ID={}, 租户ID={}",
              pricingStrategy.getStrategyName(), userContext.getUserId(), tenantContext.getTenantId());

            return processedProduct != null ? processedProduct : product;

        } catch (Exception e) {
            log.error("租户定价策略应用异常: 用户ID={}, 租户ID={}", userContext.getUserId(), tenantContext.getTenantId(), e);
            return product;
        }
    }

    @Override
    public ProductProcessingContext buildProcessingContext(Long userId, String tenantId, String platformProductId, Long productId) {
        try {
            // 构建用户定价上下文
            UserPricingContext userPricingContext = userId != null ? buildUserPricingContext(userId) : null;

            // 构建租户上下文
            TenantContext tenantContext = tenantId != null ? buildTenantContext(tenantId) : null;

            // 构建完整的处理上下文
            return ProductProcessingContext.builder()
              .userId(userId)
              .tenantId(tenantId)
              .platformProductId(platformProductId)
              .productId(productId)
              .userPricingContext(userPricingContext)
              .tenantContext(tenantContext)
              .processingStartTime(LocalDateTime.now())
              .processingMode(ProductProcessingContext.ProcessingMode.NORMAL)
              .build();

        } catch (Exception e) {
            log.error("构建处理上下文异常: userId={}, tenantId={}, platformProductId={}",
              userId, tenantId, platformProductId, e);

            // 返回最小化的上下文
            return ProductProcessingContext.builder()
              .userId(userId)
              .tenantId(tenantId)
              .platformProductId(platformProductId)
              .productId(productId)
              .processingStartTime(LocalDateTime.now())
              .build();
        }
    }

    @Override
    public UserPricingContext buildUserPricingContext(Long userId) {
        if (userId == null) {
            return null;
        }

        try {
            // TODO: 这里应该从用户服务或数据库获取用户信息
            // 当前提供默认实现
            return UserPricingContext.builder()
              .userId(userId)
              .userLevel(UserPricingContext.UserLevel.NORMAL)
              .markupRate(new BigDecimal("0.15")) // 默认15%
              .discountRate(BigDecimal.ONE) // 无折扣
              .isVip(false)
              .vipLevel(0)
              .totalPurchaseAmount(BigDecimal.ZERO)
              .totalOrderCount(0)
              .preferredCurrency("CNY")
              .build();

        } catch (Exception e) {
            log.error("构建用户定价上下文异常: userId={}", userId, e);
            return null;
        }
    }

    @Override
    public TenantContext buildTenantContext(String tenantId) {
        if (tenantId == null) {
            return null;
        }

        try {
            // TODO: 这里应该从租户服务或数据库获取租户信息
            // 当前提供默认实现
            return TenantContext.builder()
              .tenantId(tenantId)
              .tenantName("默认租户")
              .tenantType(TenantContext.TenantType.STANDARD)
              .tenantLevel(TenantContext.TenantLevel.BASIC)
              .defaultMarkupRate(new BigDecimal("0.15"))
              .minMarkupRate(new BigDecimal("0.05"))
              .maxMarkupRate(new BigDecimal("0.50"))
              .tenantDiscountRate(BigDecimal.ONE)
              .enablePersonalizedPricing(true)
              .enableDynamicPricing(false)
              .defaultCurrency("CNY")
              .build();

        } catch (Exception e) {
            log.error("构建租户上下文异常: tenantId={}", tenantId, e);
            return null;
        }
    }

    @Override
    public String getSystemStatus() {
        StringBuilder status = new StringBuilder();
        status.append("产品策略系统状态:\n");
        status.append("  处理器链状态: ").append(processorChain.getHealthStatus()).append("\n");
        status.append("  ").append(processorChain.getProcessingStatistics()).append("\n");
        status.append("  策略工厂状态: ").append(strategyFactory.getFactoryStatus()).append("\n");

        // 添加处理器信息
        status.append("  已注册处理器: ").append(processorChain.getProcessorCount()).append("个\n");
        processorChain.getProcessorInfo().forEach(info -> status.append("    - ").append(info).append("\n"));

        // 添加策略信息
        strategyFactory.getRegistrationInfo().forEach((type, strategies) -> {
            status.append("  ").append(type.getDescription()).append(": ").append(strategies.size()).append("个\n");
            strategies.forEach(strategyName -> status.append("    - ").append(strategyName).append("\n"));
        });

        return status.toString();
    }

    @Override
    public String getExecutionStatistics() {
        int total = totalProcessCount.get();
        int success = successProcessCount.get();
        int failure = failureProcessCount.get();
        double successRate = total > 0 ? (double) success / total * 100 : 0;

        StringBuilder stats = new StringBuilder();
        stats.append("产品策略执行统计:\n");
        stats.append(String.format("  总处理次数: %d\n", total));
        stats.append(String.format("  成功次数: %d\n", success));
        stats.append(String.format("  失败次数: %d\n", failure));
        stats.append(String.format("  成功率: %.2f%%\n", successRate));

        // 添加策略使用统计
        stats.append("  策略使用统计:\n");
        strategyFactory.getUsageStatistics().forEach((strategyName, count) -> stats.append(String.format("    - %s: %d次\n", strategyName, count)));

        return stats.toString();
    }

    @Override
    public void clearStrategyCache() {
        strategyFactory.clearCache();
        log.info("策略缓存已清理");
    }

    @Override
    public void resetStatistics() {
        totalProcessCount.set(0);
        successProcessCount.set(0);
        failureProcessCount.set(0);

        processorChain.resetStatistics();
        strategyFactory.resetUsageStatistics();

        log.info("策略统计已重置");
    }

    /**
     * 克隆产品数据（简化实现）
     */
    private TzProductDTO cloneProduct(TzProductDTO product) {
        try {
            // 这里应该实现深度克隆，当前简化处理
            return product;
        } catch (Exception e) {
            log.warn("克隆产品数据失败", e);
            return product;
        }
    }

    /**
     * 获取服务健康状态
     *
     * @return 健康状态
     */
    public boolean isHealthy() {
        return processorChain.getProcessorCount() > 0 &&
          strategyFactory.getStrategyCount(ProductStrategy.StrategyType.PRICING) > 0;
    }

    /**
     * 获取性能指标
     *
     * @return 性能指标字符串
     */
    public String getPerformanceMetrics() {
        int total = totalProcessCount.get();
        if (total == 0) {
            return "暂无性能数据";
        }

        double successRate = (double) successProcessCount.get() / total * 100;

        return String.format("性能指标: 总处理=%d, 成功率=%.2f%%, 处理器数=%d, 策略数=%d",
          total, successRate, processorChain.getProcessorCount(),
          strategyFactory.getStrategyCount(ProductStrategy.StrategyType.PRICING));
    }
}
