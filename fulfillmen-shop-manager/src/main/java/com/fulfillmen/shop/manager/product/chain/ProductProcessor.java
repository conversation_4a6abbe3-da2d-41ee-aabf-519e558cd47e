/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.product.chain;

import com.fulfillmen.shop.manager.product.context.ProductProcessingContext;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * 产品处理器基础抽象类
 *
 * <pre>
 * 核心职责：
 * 1. 实现责任链模式的基础框架
 * 2. 定义产品处理的统一规范和流程
 * 3. 提供异常处理和日志记录机制
 * 4. 支持处理器的动态组合和配置
 *
 * 责任链设计：
 * - 每个处理器专注单一职责
 * - 支持处理器的顺序执行和条件跳过
 * - 提供处理结果的验证和回滚机制
 * - 支持并行处理和性能优化
 *
 * 扩展机制：
 * - 易于添加新的处理器类型
 * - 支持处理器的配置化启用/禁用
 * - 提供处理器间的数据共享机制
 * </pre>
 *
 * <AUTHOR>
 * @date 2025/08/22
 * @since 1.0.0
 */
@Slf4j
public abstract class ProductProcessor {

    /**
     * 下一个处理器
     */
    protected ProductProcessor next;

    /**
     * 设置下一个处理器
     *
     * @param next 下一个处理器
     * @return 当前处理器，支持链式调用
     */
    public ProductProcessor setNext(ProductProcessor next) {
        this.next = next;
        return next;
    }

    /**
     * 处理产品数据 - 模板方法
     *
     * @param context 产品处理上下文
     * @return 处理结果
     */
    public final ProcessResult handle(ProductProcessingContext context) {
        ProcessResult result;

        try {
            // 前置检查
            if (!isApplicable(context)) {
                log.debug("处理器不适用，跳过: {}", getProcessorName());
                return continueChain(context);
            }

            // 前置处理
            preProcess(context);

            // 执行具体处理逻辑
            log.debug("开始执行处理器: {}", getProcessorName());
            result = doProcess(context);

            // 后置处理
            postProcess(context, result);

            // 如果处理成功且需要继续链式处理
            if (result.isSuccess() && shouldContinueChain(context, result)) {
                ProcessResult chainResult = continueChain(context);
                if (!chainResult.isSuccess()) {
                    result = chainResult;
                }
            }

            log.debug("处理器执行完成: {}, 结果: {}", getProcessorName(), result.isSuccess());

        } catch (Exception e) {
            log.error("处理器执行异常: {}", getProcessorName(), e);
            result = ProcessResult.failure("处理器执行异常: " + e.getMessage(), e);

            // 异常处理
            handleException(context, e);
        }

        return result;
    }

    /**
     * 继续执行链中的下一个处理器
     */
    private ProcessResult continueChain(ProductProcessingContext context) {
        if (next != null) {
            return next.handle(context);
        }
        return ProcessResult.success();
    }

    /**
     * 判断处理器是否适用于当前上下文
     *
     * @param context 处理上下文
     * @return true-适用，false-跳过
     */
    protected abstract boolean isApplicable(ProductProcessingContext context);

    /**
     * 执行具体的处理逻辑
     *
     * @param context 处理上下文
     * @return 处理结果
     */
    protected abstract ProcessResult doProcess(ProductProcessingContext context);

    /**
     * 获取处理器名称
     *
     * @return 处理器名称
     */
    protected abstract String getProcessorName();

    /**
     * 获取处理器类型
     *
     * @return 处理器类型
     */
    protected abstract ProcessorType getProcessorType();

    /**
     * 获取处理器执行顺序
     *
     * @return 执行顺序，数值越小越先执行
     */
    protected abstract int getOrder();

    /**
     * 前置处理 - 子类可重写
     *
     * @param context 处理上下文
     */
    protected void preProcess(ProductProcessingContext context) {
        // 默认实现为空，子类可重写
    }

    /**
     * 后置处理 - 子类可重写
     *
     * @param context 处理上下文
     * @param result  处理结果
     */
    protected void postProcess(ProductProcessingContext context, ProcessResult result) {
        // 默认实现为空，子类可重写
    }

    /**
     * 判断是否继续执行链中的下一个处理器
     *
     * @param context 处理上下文
     * @param result  当前处理结果
     * @return true-继续执行，false-中断链
     */
    protected boolean shouldContinueChain(ProductProcessingContext context, ProcessResult result) {
        return result.isSuccess();
    }

    /**
     * 异常处理 - 子类可重写
     *
     * @param context 处理上下文
     * @param e       异常
     */
    protected void handleException(ProductProcessingContext context, Exception e) {
        // 默认实现：记录异常到上下文
        context.setAttribute("lastException", e);
        context.setAttribute("lastFailedProcessor", getProcessorName());
    }

    /**
     * 处理器类型枚举
     */
    @Getter
    public enum ProcessorType {

        /**
         * 验证处理器
         */
        VALIDATION("验证处理器"),

        /**
         * 定价处理器
         */
        PRICING("定价处理器"),

        /**
         * 库存处理器
         */
        INVENTORY("库存处理器"),

        /**
         * 图片处理器
         */
        IMAGE("图片处理器"),

        /**
         * 增强处理器
         */
        ENHANCEMENT("增强处理器"),

        /**
         * 后置处理器
         */
        POST_PROCESSING("后置处理器");

        private final String description;

        ProcessorType(String description) {
            this.description = description;
        }

    }

    /**
     * 处理结果类
     */
    @Getter
    public static class ProcessResult {

        private final boolean success;
        private final String message;
        private final Exception exception;
        private final Object data;

        private ProcessResult(boolean success, String message, Exception exception, Object data) {
            this.success = success;
            this.message = message;
            this.exception = exception;
            this.data = data;
        }

        public static ProcessResult success() {
            return new ProcessResult(true, "处理成功", null, null);
        }

        public static ProcessResult success(String message) {
            return new ProcessResult(true, message, null, null);
        }

        public static ProcessResult success(String message, Object data) {
            return new ProcessResult(true, message, null, data);
        }

        public static ProcessResult failure(String message) {
            return new ProcessResult(false, message, null, null);
        }

        public static ProcessResult failure(String message, Exception exception) {
            return new ProcessResult(false, message, exception, null);
        }

        @Override
        public String toString() {
            return String.format("ProcessResult{success=%s, message='%s'}", success, message);
        }
    }
}
