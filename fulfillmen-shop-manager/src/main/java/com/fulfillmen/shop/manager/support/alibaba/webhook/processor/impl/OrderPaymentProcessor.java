/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.support.alibaba.webhook.processor.impl;

import com.fulfillmen.shop.dao.mapper.TzOrderItemMapper;
import com.fulfillmen.shop.dao.mapper.TzOrderPurchaseMapper;
import com.fulfillmen.shop.dao.mapper.TzOrderSupplierMapper;
import com.fulfillmen.shop.domain.entity.TzOrderPurchase;
import com.fulfillmen.shop.domain.entity.TzOrderSupplier;
import com.fulfillmen.shop.domain.entity.enums.TzOrderItemLogisticsStatusEnum;
import com.fulfillmen.shop.domain.entity.enums.TzOrderItemStatusEnum;
import com.fulfillmen.shop.domain.entity.enums.TzOrderPurchaseStatusEnum;
import com.fulfillmen.shop.domain.entity.enums.TzOrderSupplierStatusEnum;
import com.fulfillmen.shop.manager.support.alibaba.webhook.handler.OrderContextRecord;
import com.fulfillmen.shop.manager.support.alibaba.webhook.processor.OrderEventProcessor;
import com.fulfillmen.shop.manager.support.wms.IWmsManager;
import com.fulfillmen.support.alibaba.api.response.model.order.TradeBaseInfo;
import com.fulfillmen.support.alibaba.api.response.model.order.TradeBaseInfo.NewStepOrder;
import com.fulfillmen.support.alibaba.api.response.order.OrderDetailResponse.OrderDetail;
import com.fulfillmen.support.alibaba.enums.OrderMessageTypeEnums;
import com.fulfillmen.support.alibaba.enums.OrderStatusEnums;
import com.fulfillmen.support.wms.dto.enums.WmsOrderStatusEnum;
import com.fulfillmen.support.wms.dto.request.WmsPurchaseOrderDetailReq;
import com.fulfillmen.support.wms.dto.response.WmsPurchaseOrderDetailsRes;
import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionTemplate;

/**
 * 订单支付事件处理器
 * <p>
 * 策略实现类，专门处理 {@link OrderMessageTypeEnums#ORDER_BUYER_VIEW_ORDER_PAY} 和 {@link OrderMessageTypeEnums#ORDER_BATCH_PAY} 事件。
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/08/08
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OrderPaymentProcessor implements OrderEventProcessor<OrderMessageTypeEnums, OrderContextRecord> {

    private final TzOrderSupplierMapper tzOrderSupplierMapper;
    private final TzOrderPurchaseMapper tzOrderPurchaseMapper;
    private final TzOrderItemMapper tzOrderItemMapper;
    private final IWmsManager wmsManager;
    private final TransactionTemplate transactionTemplate;

    @Override
    public void process(OrderContextRecord context) {
        String orderId = context.getAlibabaOrderIdStr();
        log.info("开始处理订单支付事件: orderId={}", orderId);

        if (!isPaymentProcessable(context)) {
            log.warn("订单状态不满足支付处理条件，跳过处理。orderId={}, 1688_status={}", orderId, context.getAlibabaOrderStatus());
            return;
        }

        try {
            updateLocalOrderStatusToPaid(context);
            notifyWmsOrderPaid(context);
            log.info("订单支付事件处理完成: orderId={}", orderId);
        } catch (Exception e) {
            log.error("处理订单支付事件时发生异常: orderId={}, error={}", orderId, e.getMessage(), e);
            throw new RuntimeException("处理订单支付事件失败: orderId=" + orderId, e);
        }
    }

    /**
     * 前置条件检查：确认订单状态是否可以进行“支付”处理。 1688 的消息可能会乱序，我们收到的支付消息时，订单在1688的真实状态可能已经是“待发货”或“待收货”。
     */
    private boolean isPaymentProcessable(OrderContextRecord context) {
        OrderStatusEnums status = context.getAlibabaOrderStatus();
        return status == OrderStatusEnums.WAIT_SELLER_SEND || status == OrderStatusEnums.WAIT_BUYER_RECEIVE;
    }

    /**
     * 更新本地数据库中的订单状态为“已支付”相关状态。
     */
    private void updateLocalOrderStatusToPaid(OrderContextRecord context) {
        String orderId = context.getAlibabaOrderIdStr();
        log.debug("更新本地订单状态为已支付: orderId={}", orderId);

        TzOrderPurchase tzOrderPurchase = context.tzOrderPurchase();
        TzOrderSupplier orderSupplier = context.getTzOrderSupplierByOrderId(orderId);
        OrderDetail alibabaOrderDetail = context.alibabaOrderDetail();
        TradeBaseInfo baseInfo = alibabaOrderDetail.getBaseInfo();

        orderSupplier.setStatus(TzOrderSupplierStatusEnum.PENDING_SHIPMENT);
        orderSupplier.setPaymentDate(baseInfo.getPayTime());

        context.recalculateActualInformationToSupplierOrder(orderId);
        context.recalculateActualInformationToPurchaseOrder();

        // 判断所有供应商是否都支付完成 或 待发货
        boolean allSuppliersPendingShipment = context.tzOrderSuppliers().stream()
            .allMatch(s -> Objects.equals(s.getStatus(), TzOrderSupplierStatusEnum.PENDING_SHIPMENT));
        if (allSuppliersPendingShipment) {
            tzOrderPurchase.setOrderStatus(TzOrderPurchaseStatusEnum.PROCUREMENT_COMPLETED);
        }

        context.tzOrderItems().forEach(item -> {
            alibabaOrderDetail.getProductItems().stream()
                .filter(productItem -> Objects.equals(productItem.getSubItemIdString(), item.getPlatformItemId()))
                .findFirst().ifPresent(productItem -> {
                    item.setActualPrice(productItem.getPrice());
                    item.setQuantity(productItem.getQuantity());
                    item.setStatus(TzOrderItemStatusEnum.PROCUREMENT_IN_PROGRESS);
                    item.setLogisticsStatus(
                        TzOrderItemLogisticsStatusEnum.getByCode(productItem.getLogisticsStatus()));
                    item.setActualPaymentAmount(productItem.getItemAmount());
                });
        });

        transactionTemplate.executeWithoutResult(transactionStatus -> {
            try {
                tzOrderItemMapper.updateBatchById(context.tzOrderItems());
                tzOrderSupplierMapper.updateById(orderSupplier);
                tzOrderPurchaseMapper.updateById(tzOrderPurchase);
            } catch (Exception e) {
                log.error("更新支付状态到数据库时失败: purchaseNo={}, error={}", context.getPurchaseOrderNo(), e.getMessage(), e);
                transactionStatus.setRollbackOnly();
                throw e;
            }
        });
    }

    /**
     * 通知WMS系统订单已支付。
     */
    private void notifyWmsOrderPaid(OrderContextRecord context) {
        log.debug("通知WMS订单已支付: orderId={}", context.getAlibabaOrderIdStr());

        OrderDetail alibabaOrderDetail = context.alibabaOrderDetail();
        TradeBaseInfo baseInfo = alibabaOrderDetail.getBaseInfo();
        List<NewStepOrder> newStepOrderList = baseInfo.getNewStepOrderList();
        WmsPurchaseOrderDetailsRes wmsPurchaseOrderDetail = context.getWmsPurchaseOrderDetail();

        TzOrderSupplier orderSupplier = context.getTzOrderSupplierByOrderId(context.getAlibabaOrderIdStr());

        WmsPurchaseOrderDetailReq wmsReq = WmsPurchaseOrderDetailReq.builder()
            .purchaseNo(wmsPurchaseOrderDetail.getPurchaseNo())
            .paymentTime(baseInfo.getPayTime())
            .status(WmsOrderStatusEnum.PURCHASED_PENDING_SHIPMENT)
            .orderId(context.getAlibabaOrderId())
//          .alibabaTotalAmount(alibabaTotalAmount)
            .plusDiscount(orderSupplier.getActualPaymentPlusAmount())
            // 最终支付价格
            .alibabaFinalAmount(orderSupplier.getActualPaymentAmount())
            // 商品最终总金额
            .productFinalTotalAmount(orderSupplier.getActualPaymentGoodsAmount())
            // 最终运费
            .finalShoppingFee(orderSupplier.getActualPaymentFreightAmount())
            .couponDiscount(orderSupplier.getActualPaymentCouponAmount())
            .discount(orderSupplier.getActualPaymentDiscountAmount())
            .orderDetails(context.resyncWmsPurchaseOrderDetailsReqList())
            .build();

        this.wmsManager.updateWmsPurchaseOrder(wmsReq);
    }

    @Override
    public List<OrderMessageTypeEnums> supports() {
        // 同时支持普通支付和批量支付
        return List.of(
            OrderMessageTypeEnums.ORDER_BUYER_VIEW_ORDER_PAY,
            OrderMessageTypeEnums.ORDER_BATCH_PAY);
    }
}
