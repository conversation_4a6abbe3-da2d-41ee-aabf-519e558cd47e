/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.support.alibaba.webhook.convert;

import com.fulfillmen.shop.common.context.OrderContextDTO;
import com.fulfillmen.shop.domain.entity.TzOrderItem;
import com.fulfillmen.shop.domain.entity.TzOrderPurchase;
import com.fulfillmen.shop.domain.entity.TzOrderSupplier;
import com.fulfillmen.shop.domain.entity.enums.OrderSupplierSyncStatusEnums;
import com.fulfillmen.shop.domain.entity.enums.PlatformCodeEnum;
import com.fulfillmen.shop.domain.entity.enums.TzOrderPurchaseStatusEnum;
import com.fulfillmen.shop.domain.entity.enums.TzOrderSupplierStatusEnum;
import com.fulfillmen.starter.core.util.JacksonUtil;
import com.fulfillmen.support.alibaba.api.response.model.order.TradeBaseInfo;
import com.fulfillmen.support.alibaba.api.response.model.order.TradeNativeLogisticsInfo;
import com.fulfillmen.support.alibaba.api.response.model.order.TradeProductItem;
import com.fulfillmen.support.alibaba.api.response.order.OrderDetailResponse;
import com.fulfillmen.support.alibaba.api.response.order.OrderDetailResponse.OrderDetail;
import com.fulfillmen.support.alibaba.enums.OrderStatusEnums;
import com.fulfillmen.support.wms.dto.enums.WmsOrderStatusEnum;
import com.fulfillmen.support.wms.dto.response.WmsPurchaseOrderDetailsRes;
import com.google.common.collect.Lists;
import java.math.BigDecimal;
import java.util.List;
import me.ahoo.cosid.IdGenerator;
import me.ahoo.cosid.provider.DefaultIdGeneratorProvider;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 阿里巴巴订单转换器
 *
 * <AUTHOR>
 * @date 2025/7/19 22:21
 * @description: todo
 * @since 1.0.0
 */
@Mapper
public interface AlibabaOrderConvert {

    AlibabaOrderConvert INSTANCE = Mappers.getMapper(AlibabaOrderConvert.class);

    /**
     * 创建订单上下文
     *
     * @param orderDetailResponse alibaba 订单详情
     * @return OrderContext
     */
    default OrderContextDTO createOrderContext(OrderDetailResponse orderDetailResponse) {
        return null;
    }

    /**
     * 创建采购订单
     *
     * @param orderDetail 订单详情响应
     * @return TzOrderPurchase
     */
    default TzOrderPurchase createTzOrderPurchase(OrderDetail orderDetail, WmsPurchaseOrderDetailsRes wmsOrderDetail) {
        TradeBaseInfo baseInfo = orderDetail.getBaseInfo();
        TradeNativeLogisticsInfo logistics = orderDetail.getNativeLogistics();
        IdGenerator idGenerator = DefaultIdGeneratorProvider.INSTANCE.getRequired("safe-js");
        WmsOrderStatusEnum wmsOrderStatusEnum = wmsOrderDetail.getStatus();

        return TzOrderPurchase.builder()
            .id(idGenerator.generate())
            .purchaseOrderNo(baseInfo.getIdOfStr())
            // 旧版数据默认买家ID为0 ，也许不是平台的用户。
            .buyerId(0L)
            // 1688 订单状态
            .orderStatus(convertOrderStatus(OrderStatusEnums.fromCode(baseInfo.getStatus())))
            .orderDate(baseInfo.getCreateTime())
            // 用户支付的产品总价
            .customerGoodsAmount(wmsOrderDetail.getProductSalesTotalAmount())
            // 用户支付运费
            .customerTotalFreight(wmsOrderDetail.getShippingFee())
            // 用户支付产品+运费
            .customerTotalFreight(wmsOrderDetail.getTotal())
            // 用户支付服务费
            .serviceFee(wmsOrderDetail.getServiceFee() != null ? wmsOrderDetail.getServiceFee() : BigDecimal.ZERO)
            // 总优惠换算成元
            .payableDiscountAmount(baseInfo.getDiscount().multiply(BigDecimal.valueOf(100)))
            // 旧版数据默认汇率为0 ， 为实时汇率方式计算
            .exchangeRateSnapshot(BigDecimal.ZERO)
            // 收货地址信息 后期作废
            .deliveryAddress(logistics != null ? logistics.getAddress() : "")
            .postalCode(logistics != null ? logistics.getZip() : "")
            .province(logistics != null ? logistics.getProvince() : "")
            .city(logistics != null ? logistics.getCity() : "")
            .district(logistics != null ? logistics.getArea() : "")
            .consigneeName(logistics != null ? logistics.getContactPerson() : "")
            .consigneePhone(logistics != null ? logistics.getMobile() : "")
            // 统计信息
            // 默认一个供应商
            .supplierCount(1)
            .lineItemCount(orderDetail.getProductItems() != null ? orderDetail.getProductItems().size() : 0)
//            .totalQuantity(wmsOrderDetail.getQuantity())
            .completedSupplierCount(0)
            // 系统字段
            .gmtCreated(wmsOrderDetail.getCreateTime())
            .gmtModified(baseInfo.getModifyTime())
            .build();
    }

    /**
     * 创建供应商订单
     * TODO: 2025年07月19日 采购订单。订单项
     * <p>
     * 将 TzOrderSupplier 转换为 OrderDetailResponse
     * </p>
     *
     * @param orderDetail 订单详情响应
     * @return TzOrderSupplier
     */
    default TzOrderSupplier createTzOrderSupplier(OrderDetail orderDetail) {
        // 订单原始数据
        String metadataJson = JacksonUtil.toJsonString(orderDetail);
        // 订单基本信息
        TradeBaseInfo baseInfo = orderDetail.getBaseInfo();
        // 订单商品信息
        List<TradeProductItem> tradeProductItems = orderDetail.getProductItems();
        // 是否为 nayasource 订单编号
        boolean isNayaOrderPurchaseNo = baseInfo.getOutOrderId() != null && baseInfo.getOutOrderId().length() > 10 && baseInfo.getOutOrderId().startsWith("C");

        // 供应商订单
        TzOrderSupplier tzOrderSupplier = TzOrderSupplier.builder()
            .id(0L)
            .purchaseOrderId(0L)
//          供应商订单编号
//          .supplierOrderNo()
            .platformCode(PlatformCodeEnum.PLATFORM_CODE_1688)
            .metadataJson(metadataJson)
            .supplierId(baseInfo.getSellerId())
            .supplierName(baseInfo.getSellerLoginId())
//          .supplierShopName(baseInfo.getSellerLoginId())
            // 直接设置同步完成
            .externalSyncStatus(OrderSupplierSyncStatusEnums.SYNCED)
            .platformOrderId(baseInfo.getIdOfStr())
            .platformOrderNo(baseInfo.getOutOrderId())

            // 1688 订单金额
            .payableAmountTotal(baseInfo.getTotalAmount())
            .payableFreightAmount(baseInfo.getShippingFee())
            .payableGoodsAmount(baseInfo.getSumProductPayment())
            .build();

        return null;
    }

    default List<TzOrderItem> createTzOrderItems(OrderDetail orderDetail, Long purchaseOrderId, Long supplierOrderId) {
        List<TradeProductItem> productItems = orderDetail.getProductItems();
        List<TzOrderItem> orderItems = Lists.newArrayListWithCapacity(productItems.size());
        for (TradeProductItem productItem : productItems) {
            TzOrderItem orderItem = TzOrderItem.builder()
                .id(0L)
                .purchaseOrderId(purchaseOrderId)
                .supplierOrderId(supplierOrderId)
                .platformProductId(String.valueOf(productItem.getProductId()))
                .platformSkuId(productItem.getSkuId() != null ? String.valueOf(productItem.getSkuId()) : null)
                .platformSpecId(productItem.getSpecId() != null ? productItem.getSpecId() : null)
                .productTitle(productItem.getName())
                .price(productItem.getPrice())
                .quantity(productItem.getQuantity())
                .totalAmount(productItem.getPrice().multiply(productItem.getQuantity()))
                .gmtModified(productItem.getGmtModified())
                .gmtCreated(productItem.getGmtCreate())
                .build();
            orderItems.add(orderItem);
        }
        return orderItems;
    }

    /**
     * 1688 订单状态 转 采购订单状态
     *
     * @param orderStatusEnums 1688 订单状态
     * @return TzOrderPurchaseStatusEnum
     */
    default TzOrderPurchaseStatusEnum convertOrderStatus(OrderStatusEnums orderStatusEnums) {
        return switch (orderStatusEnums) {
            // 1688 订单状态 等待买家支付 和 等待发货 显示采购中
            case WAIT_BUYER_PAY, WAIT_SELLER_SEND -> TzOrderPurchaseStatusEnum.PROCUREMENT_IN_PROGRESS;
            // 1688 订单状态 等待买家确认收货 显示仓库待收货
            case WAIT_BUYER_RECEIVE -> TzOrderPurchaseStatusEnum.SUPPLIER_SHIPPED;
            // 1688 订单状态 已收货 显示仓库已收货
            case CONFIRM_GOODS -> TzOrderPurchaseStatusEnum.WAREHOUSE_RECEIVED;
            // 1688 订单状态 交易成功 显示已入库
            case CONFIRM_GOODS_AND_HAS_SUBSIDY -> TzOrderPurchaseStatusEnum.WAREHOUSE_RECEIVED;
            // 1688 订单状态 交易成功 显示已入库
            case SUCCESS -> TzOrderPurchaseStatusEnum.IN_STOCK;
            // 1688 订单状态 交易取消 显示已取消
            case CANCEL -> TzOrderPurchaseStatusEnum.ORDER_CANCELLED;
            // 1688 订单状态 交易终止 显示已取消
            case TERMINATED -> TzOrderPurchaseStatusEnum.ORDER_CANCELLED;
        };
    }

    /**
     * 1688 订单状态 转 供应商订单状态
     *
     * @param orderStatusEnums 1688 订单状态
     * @return TzOrderPurchaseStatusEnum
     */
    default TzOrderSupplierStatusEnum convertSupplierOrderStatus(OrderStatusEnums orderStatusEnums) {
        return switch (orderStatusEnums) {
            // 1688 订单状态 等待买家支付
            case WAIT_BUYER_PAY -> TzOrderSupplierStatusEnum.PENDING_PAYMENT;
            // 1688 订单状态 等待卖家发货 显示待发货
            case WAIT_SELLER_SEND -> TzOrderSupplierStatusEnum.PENDING_SHIPMENT;
            // 1688 订单状态 等待买家确认收货 显示仓库待收货
            case WAIT_BUYER_RECEIVE -> TzOrderSupplierStatusEnum.PENDING_SHIPMENT;
            // 1688 订单状态 已收货 显示仓库已收货
            case CONFIRM_GOODS, CONFIRM_GOODS_AND_HAS_SUBSIDY -> TzOrderSupplierStatusEnum.WAREHOUSE_PENDING_RECEIPT;
            // 1688 订单状态 已收货 显示仓库已收货
//            case CONFIRM_GOODS_AND_HAS_SUBSIDY -> TzOrderSupplierStatusEnum.WAREHOUSE_PENDING_RECEIPT;
            // 1688 订单状态 交易成功 显示已入库 -- 这里因为长时间没确认收货，自动将订单状态变更成签收了。
            case SUCCESS -> TzOrderSupplierStatusEnum.WAREHOUSE_RECEIVED;
            // 1688 订单状态 交易取消 显示已取消
            case CANCEL -> TzOrderSupplierStatusEnum.CANCELLED;
            // 1688 订单状态 交易终止 显示已取消
            case TERMINATED -> TzOrderSupplierStatusEnum.CANCELLED;
        };
    }

    /**
     * wms 订单状态 转 采购订单状态
     *
     * @param wmsOrderStatusEnum wms 订单状态
     * @return TzOrderPurchaseStatusEnum
     */
    default TzOrderPurchaseStatusEnum convertPurchaseOrderStatus(WmsOrderStatusEnum wmsOrderStatusEnum) {
        return switch (wmsOrderStatusEnum) {
            // 已付款
            case PAID_PENDING_REVIEW -> TzOrderPurchaseStatusEnum.PAYMENT_COMPLETED;
            // 待审核
            case REVIEWED_PENDING_PURCHASE -> TzOrderPurchaseStatusEnum.PROCUREMENT_IN_PROGRESS;
            // 待发货
            case PURCHASED_PENDING_SHIPMENT -> TzOrderPurchaseStatusEnum.SUPPLIER_SHIPPED;
            // 待收货
            case SHIPPED_PENDING_RECEIPT -> TzOrderPurchaseStatusEnum.WAREHOUSE_PENDING_RECEIVED;
            // 待签收
            case PENDING_SIGNATURE -> TzOrderPurchaseStatusEnum.WAREHOUSE_RECEIVED;
            // 已签收
            case SIGNED -> TzOrderPurchaseStatusEnum.IN_STOCK;
            // 已完成
            case COMPLETED -> TzOrderPurchaseStatusEnum.ORDER_COMPLETED;
            // 发货异常 暂无此方案
            case SHIPMENT_EXCEPTION -> TzOrderPurchaseStatusEnum.ORDER_CANCELLED;
            // 已作废
            case CANCELED -> TzOrderPurchaseStatusEnum.ORDER_CANCELLED;
            // 待付款
            case PENDING_PAYMENT -> TzOrderPurchaseStatusEnum.PAYMENT_PENDING;
            // 未知状态
        };
    }

//    default TzOrderItemStatusEnum convertOrderItemStatus() {
//        return switch (orderStatusEnums) {
//            // 1688 订单状态 等待买家支付
//            case WAIT_BUYER_PAY -> TzOrderItemStatusEnum.PENDING;
//            // 1688 订单状态 等待卖家发货
//            case WAIT_SELLER_SEND -> TzOrderItemStatusEnum.PROCUREMENT_IN_PROGRESS;
//            // 1688 订单状态 等待买家确认收货
//            case WAIT_BUYER_RECEIVE -> TzOrderItemStatusEnum.SHIPPED;
//            // 1688 订单状态 已收货
//            case CONFIRM_GOODS -> TzOrderItemStatusEnum.WAREHOUSE_RECEIVED;
//            // 1688 订单状态 交易成功
//            case SUCCESS -> TzOrderItemStatusEnum.COMPLETED;
//            // 1688 订单状态 交易取消
//            case CANCEL -> TzOrderItemStatusEnum.CANCELLED;
//            // 1688 订单状态 交易终止
//            case TERMINATED -> TzOrderItemStatusEnum.CANCELLED;
//        };
//    }

}
