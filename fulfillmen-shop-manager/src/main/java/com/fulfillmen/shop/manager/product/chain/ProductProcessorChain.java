/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.product.chain;

import com.fulfillmen.shop.manager.product.context.ProductProcessingContext;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 产品处理器链管理器
 *
 * <pre>
 * 核心职责：
 * 1. 管理产品处理器的注册和组织
 * 2. 构建和执行责任链
 * 3. 提供处理器的动态配置和监控
 * 4. 支持处理链的性能优化和故障处理
 *
 * 链式处理流程：
 * 1. 定价处理器 -> 2. 其他扩展处理器
 *
 * 特性：
 * - 自动按order排序构建处理链
 * - 支持处理器的动态启用/禁用
 * - 提供处理过程的监控和统计
 * - 支持处理失败时的回滚和降级
 * - 线程安全的链式处理
 * </pre>
 *
 * <AUTHOR>
 * @date 2025/08/22
 * @since 1.0.0
 */
@Slf4j
@Component
public class ProductProcessorChain {

    private final List<ProductProcessor> processors;
    private final ConcurrentHashMap<String, AtomicInteger> processingStats;
    private ProductProcessor chainHead;

    public ProductProcessorChain(List<ProductProcessor> processors) {
        this.processors = new ArrayList<>(processors);
        this.processingStats = new ConcurrentHashMap<>();
        this.buildChain();

        log.info("产品处理器链初始化完成，处理器数量: {}", processors.size());
        this.logProcessorChain();
    }

    /**
     * 执行完整的产品处理链
     *
     * @param context 产品处理上下文
     * @return 最终处理结果
     */
    public ProductProcessor.ProcessResult processProduct(ProductProcessingContext context) {
        if (context == null) {
            log.warn("产品处理链: 上下文为空");
            return ProductProcessor.ProcessResult.failure("处理上下文为空");
        }

        if (chainHead == null) {
            log.warn("产品处理链: 处理链未初始化");
            return ProductProcessor.ProcessResult.failure("处理链未初始化");
        }

        try {
            // 记录处理开始
            recordProcessingStart(context);

            log.debug("开始执行产品处理链: 产品ID={}, 用户ID={}",
              context.getProductId(), context.getUserId());

            // 执行处理链
            ProductProcessor.ProcessResult result = chainHead.handle(context);

            // 记录处理结果
            recordProcessingResult(context, result);

            // 更新统计信息
            updateProcessingStatistics(result);

            log.debug("产品处理链执行完成: 产品ID={}, 结果={}",
              context.getProductId(), result.isSuccess());

            return result;

        } catch (Exception e) {
            log.error("产品处理链执行异常: 产品ID={}", context.getProductId(), e);

            // 记录异常
            context.setAttribute("chainException", e);
            updateProcessingStatistics(ProductProcessor.ProcessResult.failure("链执行异常: " + e.getMessage(), e));

            return ProductProcessor.ProcessResult.failure("处理链执行异常: " + e.getMessage(), e);
        }
    }

    /**
     * 构建责任链
     * 按处理器的order字段排序，然后链接
     */
    private void buildChain() {
        if (processors.isEmpty()) {
            log.warn("没有可用的产品处理器");
            return;
        }

        // 按order排序
        List<ProductProcessor> sortedProcessors = processors.stream()
          .sorted(Comparator.comparing(ProductProcessor::getOrder))
          .toList();

        // 构建链
        chainHead = sortedProcessors.getFirst();
        ProductProcessor current = chainHead;

        for (int i = 1; i < sortedProcessors.size(); i++) {
            current = current.setNext(sortedProcessors.get(i));
        }

        log.debug("责任链构建完成，处理器顺序: {}",
          sortedProcessors.stream()
            .map(p -> p.getProcessorName() + "(order:" + p.getOrder() + ")")
            .toList());
    }

    /**
     * 重新构建处理链（用于动态调整）
     */
    public void rebuildChain() {
        log.info("重新构建产品处理器链");
        buildChain();
        logProcessorChain();
    }

    /**
     * 添加处理器到链中
     *
     * @param processor 新的处理器
     */
    public void addProcessor(ProductProcessor processor) {
        if (processor == null) {
            throw new IllegalArgumentException("处理器不能为空");
        }

        processors.add(processor);
        log.info("添加新的处理器: {}", processor.getProcessorName());

        // 重新构建链
        rebuildChain();
    }

    /**
     * 移除处理器
     *
     * @param processorName 处理器名称
     * @return 是否成功移除
     */
    public boolean removeProcessor(String processorName) {
        boolean removed = processors.removeIf(p -> p.getProcessorName().equals(processorName));

        if (removed) {
            log.info("移除处理器: {}", processorName);
            rebuildChain();
        } else {
            log.warn("未找到要移除的处理器: {}", processorName);
        }

        return removed;
    }

    /**
     * 记录处理开始
     */
    private void recordProcessingStart(ProductProcessingContext context) {
        context.setAttribute("chainProcessingStartTime", System.currentTimeMillis());
        context.setAttribute("chainProcessorCount", processors.size());
    }

    /**
     * 记录处理结果
     */
    private void recordProcessingResult(ProductProcessingContext context, ProductProcessor.ProcessResult result) {
        Long startTime = context.getAttribute("chainProcessingStartTime");
        if (startTime != null) {
            long duration = System.currentTimeMillis() - startTime;
            context.setAttribute("chainProcessingDuration", duration);

            if (log.isDebugEnabled()) {
                log.debug("处理链执行耗时: {}ms, 结果: {}", duration, result.isSuccess());
            }
        }

        context.setAttribute("chainFinalResult", result.isSuccess());
        context.setAttribute("chainFinalMessage", result.getMessage());
    }

    /**
     * 更新处理统计信息
     */
    private void updateProcessingStatistics(ProductProcessor.ProcessResult result) {
        String statKey = result.isSuccess() ? "success" : "failure";
        processingStats.computeIfAbsent(statKey, k -> new AtomicInteger(0)).incrementAndGet();
        processingStats.computeIfAbsent("total", k -> new AtomicInteger(0)).incrementAndGet();
    }

    /**
     * 打印处理器链信息
     */
    private void logProcessorChain() {
        if (!log.isInfoEnabled()) {
            return;
        }

        log.info("产品处理器链结构:");
        processors.stream()
          .sorted(Comparator.comparing(ProductProcessor::getOrder))
          .forEach(processor -> log.info("  {} -> {} (order: {}, type: {})",
            processor.getOrder(),
            processor.getProcessorName(),
            processor.getOrder(),
            processor.getProcessorType().getDescription()));
    }

    /**
     * 获取处理器信息
     *
     * @return 处理器信息列表
     */
    public List<String> getProcessorInfo() {
        return processors.stream()
          .sorted(Comparator.comparing(ProductProcessor::getOrder))
          .map(processor -> String.format("%s (order: %d, type: %s)",
            processor.getProcessorName(),
            processor.getOrder(),
            processor.getProcessorType().getDescription()))
          .toList();
    }

    /**
     * 获取处理统计信息
     *
     * @return 统计信息
     */
    public String getProcessingStatistics() {
        int total = processingStats.getOrDefault("total", new AtomicInteger(0)).get();
        int success = processingStats.getOrDefault("success", new AtomicInteger(0)).get();
        int failure = processingStats.getOrDefault("failure", new AtomicInteger(0)).get();

        double successRate = total > 0 ? (double) success / total * 100 : 0;

        return String.format("处理链统计: 总处理数=%d, 成功=%d, 失败=%d, 成功率=%.2f%%",
          total, success, failure, successRate);
    }

    /**
     * 重置统计信息
     */
    public void resetStatistics() {
        processingStats.clear();
        log.info("处理链统计信息已重置");
    }

    /**
     * 检查处理器链健康状态
     *
     * @return 健康状态信息
     */
    public String getHealthStatus() {
        if (processors.isEmpty()) {
            return "UNHEALTHY: 没有可用的处理器";
        }

        if (chainHead == null) {
            return "UNHEALTHY: 处理链未初始化";
        }

        // 检查是否有必要的处理器类型
        boolean hasPricingProcessor = processors.stream()
          .anyMatch(p -> p.getProcessorType() == ProductProcessor.ProcessorType.PRICING);

        if (!hasPricingProcessor) {
            return "WARNING: 缺少定价处理器";
        }

        return String.format("HEALTHY: %d个处理器正常运行", processors.size());
    }

    /**
     * 获取处理器数量
     *
     * @return 处理器数量
     */
    public int getProcessorCount() {
        return processors.size();
    }

    /**
     * 检查是否包含指定类型的处理器
     *
     * @param processorType 处理器类型
     * @return 是否包含
     */
    public boolean hasProcessorType(ProductProcessor.ProcessorType processorType) {
        return processors.stream()
          .anyMatch(p -> p.getProcessorType() == processorType);
    }

    /**
     * 获取指定类型的处理器列表
     *
     * @param processorType 处理器类型
     * @return 处理器列表
     */
    public List<ProductProcessor> getProcessorsByType(ProductProcessor.ProcessorType processorType) {
        return processors.stream()
          .filter(p -> p.getProcessorType() == processorType)
          .sorted(Comparator.comparing(ProductProcessor::getOrder))
          .toList();
    }
}
