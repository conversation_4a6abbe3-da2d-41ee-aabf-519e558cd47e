/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.product.chain.impl;

import com.fulfillmen.shop.domain.dto.TzProductDTO;
import com.fulfillmen.shop.manager.product.chain.ProductProcessor;
import com.fulfillmen.shop.manager.product.context.ProductProcessingContext;
import com.fulfillmen.shop.manager.product.strategy.pricing.PricingStrategy;
import java.util.Comparator;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 定价处理器
 *
 * <pre>
 * 核心职责：
 * 1. 在责任链中处理产品定价逻辑
 * 2. 集成多种定价策略并选择最优策略
 * 3. 提供定价过程的监控和日志记录
 * 4. 确保定价结果的合理性和一致性
 *
 * 处理流程：
 * 1. 检查产品数据和用户上下文的有效性
 * 2. 收集所有适用的定价策略
 * 3. 按优先级排序并选择最佳策略
 * 4. 执行定价计算并验证结果
 * 5. 记录定价过程和结果
 *
 * 策略选择逻辑：
 * - 优先级高的策略优先执行
 * - 只有适用的策略才会被考虑
 * - 支持策略的组合和叠加使用
 * - 提供策略执行失败的降级机制
 * </pre>
 *
 * <AUTHOR>
 * @date 2025/08/22
 * @since 1.0.0
 */
@Slf4j
@Component
public class PricingProcessor extends ProductProcessor {

    private final List<PricingStrategy> pricingStrategies;

    public PricingProcessor(List<PricingStrategy> pricingStrategies) {
        this.pricingStrategies = pricingStrategies;
        log.info("定价处理器初始化完成，加载策略数量: {}", pricingStrategies.size());

        // 打印已加载的策略
        pricingStrategies.forEach(strategy -> log.debug("已加载定价策略: {} (优先级: {}, 类型: {})",
          strategy.getStrategyName(), strategy.getPriority(), strategy.getPricingType()));
    }

    @Override
    protected boolean isApplicable(ProductProcessingContext context) {
        if (context == null) {
            log.debug("定价处理器: 上下文为空，不适用");
            return false;
        }

        // 检查是否有用户定价上下文
        if (context.getUserPricingContext() == null) {
            log.debug("定价处理器: 用户定价上下文为空，不适用");
            return false;
        }

        // 检查产品数据是否存在（通过属性获取）
        TzProductDTO product = context.getAttribute("product");
        if (product == null) {
            log.debug("定价处理器: 产品数据为空，不适用");
            return false;
        }

        return true;
    }

    @Override
    protected ProcessResult doProcess(ProductProcessingContext context) {
        try {
            // 获取产品数据
            TzProductDTO product = context.getAttribute("product");
            if (product == null) {
                return ProcessResult.failure("产品数据为空");
            }

            // 查找适用的定价策略
            List<PricingStrategy> applicableStrategies = findApplicableStrategies(context);

            if (applicableStrategies.isEmpty()) {
                log.warn("定价处理器: 没有找到适用的定价策略，使用原始价格");
                return ProcessResult.success("没有适用的定价策略，保持原始价格");
            }

            // 选择最佳策略（按优先级排序，选择第一个）
            PricingStrategy selectedStrategy = selectBestStrategy(applicableStrategies);

            log.debug("定价处理器: 选择策略 {} (优先级: {})",
              selectedStrategy.getStrategyName(), selectedStrategy.getPriority());

            // 应用定价策略
            TzProductDTO processedProduct = applyPricingStrategy(product, selectedStrategy, context);

            // 更新产品数据到上下文
            context.setAttribute("product", processedProduct);
            context.setAttribute("appliedPricingStrategy", selectedStrategy.getStrategyName());
            context.setAttribute("pricingStrategyType", selectedStrategy.getPricingType().getName());

            // 记录定价结果
            logPricingResult(product, processedProduct, selectedStrategy, context);

            return ProcessResult.success("定价处理完成，应用策略: " + selectedStrategy.getStrategyName());

        } catch (Exception e) {
            log.error("定价处理器执行异常", e);
            return ProcessResult.failure("定价处理异常: " + e.getMessage(), e);
        }
    }

    @Override
    protected String getProcessorName() {
        return "定价处理器";
    }

    @Override
    protected ProcessorType getProcessorType() {
        return ProcessorType.PRICING;
    }

    @Override
    protected int getOrder() {
        // 定价处理器执行顺序
        return 20;
    }

    /**
     * 查找适用的定价策略
     *
     * @param context 处理上下文
     * @return 适用的策略列表
     */
    private List<PricingStrategy> findApplicableStrategies(ProductProcessingContext context) {
        return pricingStrategies.stream()
          .filter(strategy -> {
              try {
                  boolean applicable = strategy.isApplicable(context);
                  log.debug("策略适用性检查: {} = {}", strategy.getStrategyName(), applicable);
                  return applicable;
              } catch (Exception e) {
                  log.warn("策略适用性检查异常: {}", strategy.getStrategyName(), e);
                  return false;
              }
          })
          .toList();
    }

    /**
     * 选择最佳策略 按优先级排序，选择优先级最高的策略
     *
     * @param applicableStrategies 适用的策略列表
     * @return 最佳策略
     */
    private PricingStrategy selectBestStrategy(List<PricingStrategy> applicableStrategies) {
        return applicableStrategies.stream()
          .min(Comparator.comparing(PricingStrategy::getPriority))
          .orElseThrow(() -> new IllegalStateException("无法选择定价策略"));
    }

    /**
     * 应用定价策略
     *
     * @param product  原始产品数据
     * @param strategy 选择的策略
     * @param context  处理上下文
     * @return 处理后的产品数据
     */
    private TzProductDTO applyPricingStrategy(TzProductDTO product, PricingStrategy strategy, ProductProcessingContext context) {
        try {
            // 记录原始价格信息（用于对比）
            recordOriginalPrices(product, context);

            // 应用策略
            TzProductDTO processedProduct = strategy.process(product, context);

            // 验证处理结果
            if (processedProduct == null) {
                log.warn("定价策略返回空产品，使用原始产品数据");
                return product;
            }

            // 验证价格合理性
            if (!validatePricingResult(product, processedProduct, strategy, context)) {
                log.warn("定价结果验证失败，使用原始产品数据");
                return product;
            }

            return processedProduct;

        } catch (Exception e) {
            log.error("应用定价策略异常: {}", strategy.getStrategyName(), e);
            // 策略执行异常时，返回原始产品数据
            return product;
        }
    }

    /**
     * 记录原始价格信息
     */
    private void recordOriginalPrices(TzProductDTO product, ProductProcessingContext context) {
        if (product.getSkuList() != null && !product.getSkuList().isEmpty()) {
            // 记录第一个SKU的原始价格作为参考
            var firstSku = product.getSkuList().get(0);
            context.setAttribute("originalPrice", firstSku.getPrice());
            context.setAttribute("originalDropShippingPrice", firstSku.getDropShippingPrice());
        }
    }

    /**
     * 验证定价结果的合理性
     */
    private boolean validatePricingResult(TzProductDTO originalProduct,
      TzProductDTO processedProduct,
      PricingStrategy strategy,
      ProductProcessingContext context) {
        try {
            // 基础验证
            if (processedProduct.getSkuList() == null || processedProduct.getSkuList().isEmpty()) {
                log.warn("处理后的产品SKU列表为空");
                return false;
            }

            // 价格合理性验证
            for (int i = 0; i < processedProduct.getSkuList().size(); i++) {
                var processedSku = processedProduct.getSkuList().get(i);
                var originalSku = originalProduct.getSkuList().get(i);

                // 验证价格不为负数
                if (processedSku.getPrice() != null && processedSku.getPrice().compareTo(java.math.BigDecimal.ZERO) < 0) {
                    log.warn("SKU价格为负数: {}", processedSku.getPrice());
                    return false;
                }

                // 使用策略的验证方法
                if (originalSku.getPrice() != null && processedSku.getPrice() != null) {
                    if (!strategy.validatePrice(originalSku.getPrice(), processedSku.getPrice(), context.getUserPricingContext())) {
                        log.warn("策略价格验证失败: {} -> {}", originalSku.getPrice(), processedSku.getPrice());
                        return false;
                    }
                }
            }

            return true;

        } catch (Exception e) {
            log.error("定价结果验证异常", e);
            return false;
        }
    }

    /**
     * 记录定价结果
     */
    private void logPricingResult(TzProductDTO originalProduct,
      TzProductDTO processedProduct,
      PricingStrategy strategy,
      ProductProcessingContext context) {
        if (!log.isInfoEnabled()) {
            return;
        }

        try {
            if (originalProduct.getSkuList() != null && !originalProduct.getSkuList().isEmpty() &&
              processedProduct.getSkuList() != null && !processedProduct.getSkuList().isEmpty()) {

                var originalSku = originalProduct.getSkuList().get(0);
                var processedSku = processedProduct.getSkuList().get(0);

                if (originalSku.getPrice() != null && processedSku.getPrice() != null) {
                    String description = strategy.getPriceAdjustmentDescription(
                      originalSku.getPrice(), processedSku.getPrice(), context.getUserPricingContext());

                    log.info("定价处理完成: 产品ID={}, 用户ID={}, {}",
                      context.getProductId(), context.getUserId(), description);
                }
            }

        } catch (Exception e) {
            log.warn("记录定价结果异常", e);
        }
    }

    @Override
    protected void postProcess(ProductProcessingContext context, ProcessResult result) {
        // 记录定价处理的统计信息
        context.setAttribute("pricingProcessorResult", result.isSuccess());
        context.setAttribute("pricingProcessorMessage", result.getMessage());

        if (result.isSuccess()) {
            log.debug("定价处理器执行成功: {}", result.getMessage());
        } else {
            log.warn("定价处理器执行失败: {}", result.getMessage());
        }
    }

    /**
     * 获取可用策略信息
     *
     * @return 策略信息列表
     */
    public List<String> getAvailableStrategies() {
        return pricingStrategies.stream()
          .map(strategy -> String.format("%s (优先级: %d, 类型: %s)",
            strategy.getStrategyName(), strategy.getPriority(), strategy.getPricingType()))
          .toList();
    }

    /**
     * 获取策略统计信息
     *
     * @return 统计信息
     */
    public String getStrategyStatistics() {
        return String.format("定价处理器统计: 总策略数=%d, 默认策略数=%d, VIP策略数=%d, 租户策略数=%d",
          pricingStrategies.size(),
          pricingStrategies.stream().mapToInt(s -> s.getPricingType() == PricingStrategy.PricingStrategyType.DEFAULT ? 1 : 0).sum(),
          pricingStrategies.stream().mapToInt(s -> s.getPricingType() == PricingStrategy.PricingStrategyType.VIP ? 1 : 0).sum(),
          pricingStrategies.stream().mapToInt(s -> s.getPricingType() == PricingStrategy.PricingStrategyType.TENANT ? 1 : 0).sum());
    }
}
