/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.product.context;

import java.math.BigDecimal;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;

/**
 * 租户上下文
 *
 * <pre>
 * 核心职责：
 * 1. 承载租户相关的配置和策略信息
 * 2. 支持多租户环境下的差异化配置
 * 3. 提供租户级别的定价、库存等策略参数
 * 4. 支持租户个性化的业务规则
 *
 * 应用场景：
 * - 不同租户的定价策略配置
 * - 租户专属的折扣和加价规则
 * - 租户级别的功能开关
 * - 租户品牌化和个性化设置
 * </pre>
 *
 * <AUTHOR>
 * @date 2025/08/22
 * @since 1.0.0
 */
@Data
@Builder
public class TenantContext {

    /**
     * 租户ID
     */
    private String tenantId;

    /**
     * 租户名称
     */
    private String tenantName;

    /**
     * 租户类型
     */
    @Builder.Default
    private TenantType tenantType = TenantType.STANDARD;

    /**
     * 租户等级
     */
    @Builder.Default
    private TenantLevel tenantLevel = TenantLevel.BASIC;

    /**
     * 租户默认加价率
     */
    @Builder.Default
    private BigDecimal defaultMarkupRate = new BigDecimal("0.15");

    /**
     * 租户最小加价率
     */
    @Builder.Default
    private BigDecimal minMarkupRate = new BigDecimal("0.05");

    /**
     * 租户最大加价率
     */
    @Builder.Default
    private BigDecimal maxMarkupRate = new BigDecimal("0.50");

    /**
     * 租户专属折扣率
     */
    @Builder.Default
    private BigDecimal tenantDiscountRate = BigDecimal.ONE;

    /**
     * 是否启用个性化定价
     */
    @Builder.Default
    private boolean enablePersonalizedPricing = true;

    /**
     * 是否启用动态定价
     */
    @Builder.Default
    private boolean enableDynamicPricing = false;

    /**
     * 支持的货币列表
     */
    @Builder.Default
    private String supportedCurrencies = "CNY,USD,EUR";

    /**
     * 默认货币
     */
    @Builder.Default
    private String defaultCurrency = "CNY";

    /**
     * 租户配置属性
     */
    @Builder.Default
    private Map<String, Object> configurations = new ConcurrentHashMap<>();

    /**
     * 功能开关
     */
    @Builder.Default
    private Map<String, Boolean> featureFlags = new ConcurrentHashMap<>();

    /**
     * 租户类型枚举
     */
    @Getter
    public enum TenantType {

        /**
         * 标准租户
         */
        STANDARD("标准租户"),

        /**
         * 企业租户
         */
        ENTERPRISE("企业租户"),

        /**
         * 合作伙伴
         */
        PARTNER("合作伙伴"),

        /**
         * 试用租户
         */
        TRIAL("试用租户");

        private final String description;

        TenantType(String description) {
            this.description = description;
        }

    }

    /**
     * 租户等级枚举
     */
    @Getter
    public enum TenantLevel {

        /**
         * 基础版
         */
        BASIC("基础版", new BigDecimal("0.15"), 1000),

        /**
         * 专业版
         */
        PROFESSIONAL("专业版", new BigDecimal("0.12"), 5000),

        /**
         * 企业版
         */
        ENTERPRISE("企业版", new BigDecimal("0.10"), 20000),

        /**
         * 旗舰版
         */
        PREMIUM("旗舰版", new BigDecimal("0.08"), 100000);

        private final String description;
        private final BigDecimal recommendedMarkupRate;
        private final Integer monthlyQuota;

        TenantLevel(String description, BigDecimal recommendedMarkupRate, Integer monthlyQuota) {
            this.description = description;
            this.recommendedMarkupRate = recommendedMarkupRate;
            this.monthlyQuota = monthlyQuota;
        }

    }

    /**
     * 获取配置属性
     *
     * @param key 配置键
     * @param <T> 配置值类型
     * @return 配置值
     */
    @SuppressWarnings("unchecked")
    public <T> T getConfiguration(String key) {
        return (T) configurations.get(key);
    }

    /**
     * 获取配置属性，如果不存在则返回默认值
     *
     * @param key          配置键
     * @param defaultValue 默认值
     * @param <T>          配置值类型
     * @return 配置值或默认值
     */
    @SuppressWarnings("unchecked")
    public <T> T getConfiguration(String key, T defaultValue) {
        return (T) configurations.getOrDefault(key, defaultValue);
    }

    /**
     * 设置配置属性
     *
     * @param key   配置键
     * @param value 配置值
     */
    public void setConfiguration(String key, Object value) {
        this.configurations.put(key, value);
    }

    /**
     * 检查功能开关
     *
     * @param feature 功能名称
     * @return true-启用，false-禁用
     */
    public boolean isFeatureEnabled(String feature) {
        return featureFlags.getOrDefault(feature, false);
    }

    /**
     * 设置功能开关
     *
     * @param feature 功能名称
     * @param enabled 是否启用
     */
    public void setFeatureFlag(String feature, boolean enabled) {
        this.featureFlags.put(feature, enabled);
    }

    /**
     * 获取有效的加价率 如果租户有自定义加价率，使用自定义的；否则使用等级推荐的
     *
     * @return 有效加价率
     */
    public BigDecimal getEffectiveMarkupRate() {
        if (defaultMarkupRate != null && defaultMarkupRate.compareTo(BigDecimal.ZERO) > 0) {
            // 确保在允许范围内
            if (defaultMarkupRate.compareTo(minMarkupRate) < 0) {
                return minMarkupRate;
            }
            if (defaultMarkupRate.compareTo(maxMarkupRate) > 0) {
                return maxMarkupRate;
            }
            return defaultMarkupRate;
        }
        return tenantLevel.getRecommendedMarkupRate();
    }

    /**
     * 常用配置键常量
     */
    public static class ConfigKeys {

        public static final String ENABLE_BULK_DISCOUNT = "enableBulkDiscount";
        public static final String BULK_DISCOUNT_THRESHOLD = "bulkDiscountThreshold";
        public static final String BULK_DISCOUNT_RATE = "bulkDiscountRate";
        public static final String ENABLE_LOYALTY_PRICING = "enableLoyaltyPricing";
        public static final String MAX_DISCOUNT_RATE = "maxDiscountRate";
        public static final String PRICE_DISPLAY_FORMAT = "priceDisplayFormat";
        public static final String ENABLE_PRICE_COMPARISON = "enablePriceComparison";
    }

    /**
     * 常用功能开关常量
     */
    public static class FeatureFlags {

        public static final String PERSONALIZED_PRICING = "personalizedPricing";
        public static final String DYNAMIC_PRICING = "dynamicPricing";
        public static final String BULK_PRICING = "bulkPricing";
        public static final String VIP_PRICING = "vipPricing";
        public static final String PROMOTIONAL_PRICING = "promotionalPricing";
        public static final String REGIONAL_PRICING = "regionalPricing";
    }
}
