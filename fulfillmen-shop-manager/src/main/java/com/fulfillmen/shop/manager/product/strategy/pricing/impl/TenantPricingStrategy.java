/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.product.strategy.pricing.impl;

import com.fulfillmen.shop.manager.product.context.ProductProcessingContext;
import com.fulfillmen.shop.manager.product.context.TenantContext;
import com.fulfillmen.shop.manager.product.context.UserPricingContext;
import com.fulfillmen.shop.manager.product.strategy.pricing.PricingStrategy;
import java.math.BigDecimal;
import java.math.RoundingMode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 租户定价策略实现
 *
 * <pre>
 * 核心功能：
 * 1. 基于租户等级和配置提供差异化定价
 * 2. 支持租户专属的加价率和折扣规则
 * 3. 提供企业客户的批量定价优惠
 * 4. 支持租户级别的个性化定价策略
 *
 * 租户定价逻辑：
 * - 基础版：15%加价 (标准定价)
 * - 专业版：12%加价 (8%优惠)
 * - 企业版：10%加价 (33%优惠)
 * - 旗舰版：8%加价 (47%优惠)
 *
 * 优惠策略：
 * - 租户等级折扣：基于订阅等级
 * - 租户专属折扣：基于合同协议
 * - 批量采购优惠：基于订单规模
 * - 长期合作奖励：基于合作历史
 *
 * 适用条件：
 * - 必须有有效的租户上下文
 * - 租户启用个性化定价
 * - 租户等级 >= BASIC
 * </pre>
 *
 * <AUTHOR>
 * @date 2025/08/22
 * @since 1.0.0
 */
@Slf4j
@Component
public class TenantPricingStrategy implements PricingStrategy {

    /**
     * 是否启用租户定价策略
     */
    @Value("${fulfillmen.product.pricing.tenant.enabled:true}")
    private boolean tenantPricingEnabled;

    /**
     * 企业租户额外折扣率
     */
    @Value("${fulfillmen.product.pricing.tenant.enterprise-discount:0.02}")
    private BigDecimal enterpriseDiscount;

    /**
     * 合作伙伴特殊折扣率
     */
    @Value("${fulfillmen.product.pricing.tenant.partner-discount:0.03}")
    private BigDecimal partnerDiscount;

    /**
     * 批量采购折扣阈值（订单金额）
     */
    @Value("${fulfillmen.product.pricing.tenant.bulk-threshold:10000}")
    private BigDecimal bulkDiscountThreshold;

    /**
     * 批量采购折扣率
     */
    @Value("${fulfillmen.product.pricing.tenant.bulk-discount-rate:0.01}")
    private BigDecimal bulkDiscountRate;

    /**
     * 价格精度（小数位数）
     */
    @Value("${fulfillmen.product.pricing.decimal-places:2}")
    private int decimalPlaces;

    @Override
    public BigDecimal calculatePrice(BigDecimal originalPrice, UserPricingContext context) {
        if (originalPrice == null || originalPrice.compareTo(BigDecimal.ZERO) <= 0) {
            log.warn("租户定价策略: 原始价格无效，返回零价格: {}", originalPrice);
            return BigDecimal.ZERO;
        }

        // 需要通过ProductProcessingContext获取租户上下文
        // 这里简化处理，实际应用中会从context中获取
        return originalPrice;
    }

    /**
     * 计算租户定价（包含租户上下文）
     *
     * @param originalPrice 原始价格
     * @param userContext   用户定价上下文
     * @param tenantContext 租户上下文
     * @return 计算后的价格
     */
    public BigDecimal calculateTenantPrice(BigDecimal originalPrice,
      UserPricingContext userContext,
      TenantContext tenantContext) {
        if (originalPrice == null || originalPrice.compareTo(BigDecimal.ZERO) <= 0) {
            log.warn("租户定价策略: 原始价格无效，返回零价格: {}", originalPrice);
            return BigDecimal.ZERO;
        }

        if (tenantContext == null) {
            log.debug("租户定价策略: 租户上下文为空，返回原价");
            return originalPrice;
        }

        try {
            // 1. 获取租户基础加价率
            BigDecimal tenantMarkupRate = tenantContext.getEffectiveMarkupRate();

            // 2. 计算基础租户价格
            BigDecimal markup = originalPrice.multiply(tenantMarkupRate);
            BigDecimal tenantPrice = originalPrice.add(markup);

            // 3. 应用租户类型折扣
            tenantPrice = applyTenantTypeDiscount(tenantPrice, originalPrice, tenantContext);

            // 4. 应用租户专属折扣
            tenantPrice = applyTenantSpecificDiscount(tenantPrice, originalPrice, tenantContext);

            // 5. 应用批量采购优惠
            tenantPrice = applyBulkDiscount(tenantPrice, originalPrice, userContext, tenantContext);

            // 6. 应用价格精度
            tenantPrice = tenantPrice.setScale(decimalPlaces, RoundingMode.HALF_UP);

            // 记录租户定价日志
            if (log.isDebugEnabled()) {
                log.debug("租户定价策略计算: 租户ID={}, 租户等级={}, 原价={}, 基础加价率={}%, 最终价格={}",
                  tenantContext.getTenantId(), tenantContext.getTenantLevel(), originalPrice,
                  tenantMarkupRate.multiply(new BigDecimal("100")), tenantPrice);
            }

            return tenantPrice;

        } catch (Exception e) {
            log.error("租户定价策略计算异常: 原价={}, 租户ID={}", originalPrice, tenantContext.getTenantId(), e);
            // 异常时返回原价
            return originalPrice;
        }
    }

    @Override
    public boolean isApplicable(ProductProcessingContext context) {
        if (!tenantPricingEnabled) {
            log.debug("租户定价策略已禁用");
            return false;
        }

        if (context == null || context.getTenantContext() == null) {
            return false;
        }

        TenantContext tenantContext = context.getTenantContext();

        // 检查租户是否启用个性化定价
        boolean isApplicable = tenantContext.isEnablePersonalizedPricing() &&
          tenantContext.getTenantLevel() != null;

        if (log.isDebugEnabled()) {
            log.debug("租户定价策略适用性检查: 租户ID={}, 启用个性化定价={}, 租户等级={}, 适用={}",
              tenantContext.getTenantId(), tenantContext.isEnablePersonalizedPricing(),
              tenantContext.getTenantLevel(), isApplicable);
        }

        return isApplicable;
    }

    @Override
    public PricingStrategyType getPricingType() {
        return PricingStrategyType.TENANT;
    }

    @Override
    public int getPriority() {
        // 租户策略优先级高于默认策略，但低于VIP策略
        return 30;
    }

    @Override
    public String getStrategyName() {
        return "租户定价策略";
    }

    /**
     * 应用租户类型折扣
     *
     * @param currentPrice  当前价格
     * @param originalPrice 原始价格
     * @param tenantContext 租户上下文
     * @return 应用折扣后的价格
     */
    private BigDecimal applyTenantTypeDiscount(BigDecimal currentPrice,
      BigDecimal originalPrice,
      TenantContext tenantContext) {
        BigDecimal discount = BigDecimal.ZERO;

        switch (tenantContext.getTenantType()) {
            case ENTERPRISE -> {
                discount = originalPrice.multiply(enterpriseDiscount);
                log.debug("企业租户折扣: {}", discount);
            }
            case PARTNER -> {
                discount = originalPrice.multiply(partnerDiscount);
                log.debug("合作伙伴折扣: {}", discount);
            }
            default -> {
                // 标准租户和试用租户无额外折扣
            }
        }

        return currentPrice.subtract(discount);
    }

    /**
     * 应用租户专属折扣
     *
     * @param currentPrice  当前价格
     * @param originalPrice 原始价格
     * @param tenantContext 租户上下文
     * @return 应用折扣后的价格
     */
    private BigDecimal applyTenantSpecificDiscount(BigDecimal currentPrice,
      BigDecimal originalPrice,
      TenantContext tenantContext) {
        // 检查租户是否有专属折扣配置
        BigDecimal tenantDiscount = tenantContext.getTenantDiscountRate();
        if (tenantDiscount != null && tenantDiscount.compareTo(BigDecimal.ONE) < 0) {
            BigDecimal discountAmount = currentPrice.multiply(BigDecimal.ONE.subtract(tenantDiscount));
            log.debug("租户专属折扣: {}%，折扣金额: {}",
              BigDecimal.ONE.subtract(tenantDiscount).multiply(new BigDecimal("100")), discountAmount);
            return currentPrice.subtract(discountAmount);
        }

        return currentPrice;
    }

    /**
     * 应用批量采购优惠
     *
     * @param currentPrice  当前价格
     * @param originalPrice 原始价格
     * @param userContext   用户上下文
     * @param tenantContext 租户上下文
     * @return 应用优惠后的价格
     */
    private BigDecimal applyBulkDiscount(BigDecimal currentPrice, BigDecimal originalPrice, UserPricingContext userContext, TenantContext tenantContext) {
        // 检查是否启用批量折扣
        if (!tenantContext.isFeatureEnabled(TenantContext.FeatureFlags.BULK_PRICING)) {
            return currentPrice;
        }

        // 检查用户采购金额是否达到批量折扣阈值
        if (userContext != null && userContext.getTotalPurchaseAmount() != null &&
          userContext.getTotalPurchaseAmount().compareTo(bulkDiscountThreshold) >= 0) {

            BigDecimal bulkDiscount = originalPrice.multiply(bulkDiscountRate);
            log.debug("批量采购优惠: 采购金额={}, 阈值={}, 优惠金额={}",
              userContext.getTotalPurchaseAmount(), bulkDiscountThreshold, bulkDiscount);
            return currentPrice.subtract(bulkDiscount);
        }

        return currentPrice;
    }

    /**
     * 获取租户等级的优惠说明
     *
     * @param tenantLevel 租户等级
     * @return 优惠说明
     */
    public String getTenantBenefitDescription(TenantContext.TenantLevel tenantLevel) {
        BigDecimal markupRate = tenantLevel.getRecommendedMarkupRate();
        BigDecimal defaultRate = new BigDecimal("0.15");
        BigDecimal savings = defaultRate.subtract(markupRate);
        BigDecimal savingsPercentage = savings.divide(defaultRate, 4, RoundingMode.HALF_UP)
          .multiply(new BigDecimal("100"));

        return String.format("%s专享：相比基础版节省%.1f%%加价", tenantLevel.getDescription(), savingsPercentage);
    }

    /**
     * 计算租户相比基础版的节省金额
     *
     * @param originalPrice 原始价格
     * @param tenantLevel   租户等级
     * @return 节省金额
     */
    public BigDecimal calculateTenantSavings(BigDecimal originalPrice, TenantContext.TenantLevel tenantLevel) {
        if (originalPrice == null || originalPrice.compareTo(BigDecimal.ZERO) <= 0) {
            return BigDecimal.ZERO;
        }

        BigDecimal basicMarkup = originalPrice.multiply(TenantContext.TenantLevel.BASIC.getRecommendedMarkupRate());
        BigDecimal tenantMarkup = originalPrice.multiply(tenantLevel.getRecommendedMarkupRate());

        return basicMarkup.subtract(tenantMarkup).setScale(decimalPlaces, RoundingMode.HALF_UP);
    }

    @Override
    public String getPriceAdjustmentDescription(BigDecimal originalPrice, BigDecimal finalPrice, UserPricingContext context) {
        // 注意：这里需要租户上下文，实际实现中应该从ProcessingContext获取
        return String.format("应用租户定价策略 (%.2f → %.2f)", originalPrice, finalPrice);
    }

    /**
     * 获取策略配置信息
     *
     * @return 配置信息字符串
     */
    public String getConfigurationInfo() {
        return String.format("租户定价策略配置: 启用=%s, 企业折扣=%.1f%%, 合作伙伴折扣=%.1f%%, 批量采购阈值=%.0f, 批量折扣率=%.1f%%",
          tenantPricingEnabled,
          enterpriseDiscount.multiply(new BigDecimal("100")),
          partnerDiscount.multiply(new BigDecimal("100")),
          bulkDiscountThreshold,
          bulkDiscountRate.multiply(new BigDecimal("100")));
    }
}
