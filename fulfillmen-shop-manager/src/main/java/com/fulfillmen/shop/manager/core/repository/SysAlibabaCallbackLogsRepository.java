/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.core.repository;

import com.baomidou.mybatisplus.extension.repository.IRepository;
import com.fulfillmen.shop.domain.entity.SysAlibabaCallbackLogs;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/8/8 09:36
 * @description: todo
 * @since 1.0.0
 */
public interface SysAlibabaCallbackLogsRepository extends IRepository<SysAlibabaCallbackLogs> {

    /**
     * 创建 PROCESSING 日志并返回日志ID（兼容旧方法）
     *
     * @param message    消息内容
     * @param signature  签名
     * @param eventType  事件类型
     * @param orderId    订单ID
     * @param receivedAt 消息时间
     * @return 日志ID
     */
    Long createProcessingLog(String message, String signature, String eventType, String orderId, LocalDateTime receivedAt);

    /**
     * 异步标记成功
     *
     * @param logId 日志ID
     */
    void markSuccess(Long logId);

    /**
     * 异步标记失败
     *
     * @param logId  日志ID
     * @param reason 失败原因
     */
    void markFailed(Long logId, String reason);

    /**
     * 查询需要重试的失败记录
     *
     * @param maxRetryCount 最大重试次数
     * @param timeHours     时间范围（小时）
     * @param limit         限制数量
     * @return 失败记录列表
     */
    List<SysAlibabaCallbackLogs> findFailedLogsForRetry(int maxRetryCount, int timeHours, int limit);

    /**
     * 更新重试次数并标记为处理中
     *
     * @param logId      日志ID
     * @param retryCount 新的重试次数
     */
    void updateRetryCount(Long logId, int retryCount);

    /**
     * 根据订单ID查找最新的回调记录
     *
     * @param orderId 订单ID
     * @return 回调记录
     */
    SysAlibabaCallbackLogs findLatestByOrderId(Long orderId);

    /**
     * 统计失败记录数量
     *
     * @param timeHours 时间范围（小时）
     * @return 失败记录数量
     */
    long countFailedLogs(int timeHours);

    /**
     * 查找6小时内未处理的记录 - 按订单ID分组，每组取最新的业务时间戳记录
     *
     * @param limit        限制数量
     * @param processHours 处理时间范围（小时）
     * @return 每个订单ID的最新未处理记录列表（6小时内创建的）
     */
    List<SysAlibabaCallbackLogs> findLatestUnprocessedLogsByOrderId(int limit, int processHours);

    /**
     * 查找指定时间内所有未处理的记录 - 返回所有未处理记录而非仅最新的
     *
     * @param limit        限制数量
     * @param processHours 处理时间范围（小时）
     * @return 指定时间内的所有未处理记录列表，按订单ID和业务时间戳排序
     */
    List<SysAlibabaCallbackLogs> findAllUnprocessedLogsByOrderId(int limit, int processHours);

    /**
     * 标记记录为已处理
     *
     * @param logId 日志ID
     */
    void markProcessed(Long logId);

    /**
     * 将指定订单ID的所有未处理记录标记为已处理（基于最新的gmtBornVirtual时间戳） 用于批量处理：处理完最新记录后，将该订单的历史未处理记录都标记为已处理
     *
     * @param orderId              订单ID
     * @param latestGmtBornVirtual 最新处理的业务时间戳
     * @return 更新的记录数量
     */
    int markOrderProcessedByLatestTimestamp(Long orderId, Long latestGmtBornVirtual);
}
