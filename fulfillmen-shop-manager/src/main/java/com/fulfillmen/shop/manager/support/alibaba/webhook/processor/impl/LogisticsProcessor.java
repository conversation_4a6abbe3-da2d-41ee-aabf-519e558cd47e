/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.support.alibaba.webhook.processor.impl;

import com.fulfillmen.shop.domain.entity.TzOrderItem;
import com.fulfillmen.shop.domain.entity.TzOrderSupplier;
import com.fulfillmen.shop.domain.entity.enums.TzOrderItemLogisticsStatusEnum;
import com.fulfillmen.shop.manager.support.alibaba.webhook.handler.LogisticsContextRecord;
import com.fulfillmen.shop.manager.support.alibaba.webhook.handler.OrderContextRecord;
import com.fulfillmen.shop.manager.support.alibaba.webhook.processor.OrderEventProcessor;
import com.fulfillmen.support.alibaba.enums.LogisticsMessageTypeEnums;
import com.fulfillmen.support.alibaba.webhook.data.LogisticsMessage;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 物流事件处理器
 * TODO: 2025 年 08 月 21 日 16:37:17 暂时没有开启。
 * <p>
 * 处理物流相关的webhook消息，包括：
 * 1. 物流轨迹更新 (LOGISTICS_BUYER_VIEW_TRACE)
 * 2. 物流单号变更 (LOGISTICS_MAIL_NO_CHANGE)
 * </p>
 *
 * <AUTHOR>
 * @date 2025/8/21 09:58
 * @description: 物流事件处理器实现
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class LogisticsProcessor implements OrderEventProcessor<LogisticsMessageTypeEnums, LogisticsContextRecord> {

    @Override
    public void process(LogisticsContextRecord context) {
        // 这个方法用于兼容现有的处理器接口
        // 实际的物流处理逻辑在 processLogistics 方法中
        LogisticsMessage logisticsMessage = context.logisticsMessage();
        OrderContextRecord orderContext = context.orderContextRecord();
        String logisticsId = context.logisticsId();

        log.info("开始处理物流业务逻辑: logisticsId={}, orderId={}",
            logisticsId, orderContext.getAlibabaOrderIdStr());

        try {
            // 根据物流消息类型进行不同的处理
            LogisticsMessageTypeEnums messageType = determineMessageType(logisticsMessage);

            switch (messageType) {
                case LOGISTICS_BUYER_VIEW_TRACE -> processLogisticsTrace(context);
                case LOGISTICS_MAIL_NO_CHANGE -> processLogisticsMailNoChange(context);
                default -> log.warn("不支持的物流消息类型: {}", messageType);
            }

            log.info("物流业务逻辑处理完成: logisticsId={}, orderId={}",
                logisticsId, orderContext.getAlibabaOrderIdStr());

        } catch (Exception e) {
            log.error("处理物流业务逻辑失败: logisticsId={}, orderId={}, error={}",
                logisticsId, orderContext.getAlibabaOrderIdStr(), e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 处理物流轨迹更新
     */
    private void processLogisticsTrace(LogisticsContextRecord logisticsContext) {
        OrderContextRecord orderContext = logisticsContext.orderContextRecord();
        String orderId = logisticsContext.logisticsOrderId();
        String orderEntryId = logisticsContext.logisticsOrderEntryId();

        log.info("处理物流轨迹更新: orderId={}, orderEntryId={}", orderId, orderEntryId);

        try {
            // 1. 更新订单物流状态
            updateOrderLogisticsStatus(orderContext, logisticsContext);

            // 2. 更新订单项物流状态
            updateOrderItemLogisticsStatus(orderContext, logisticsContext);

            // 3. 更新 WMS 采购订单物流状态
            updateWmsPurchaseOrderLogisticsStatus(orderContext, logisticsContext);

            // 4. 同步物流信息到供应商订单
            syncLogisticsToSupplierOrder(orderContext, logisticsContext);

            log.info("物流轨迹更新完成: orderId={}, orderEntryId={}", orderId, orderEntryId);

        } catch (Exception e) {
            log.error("处理物流轨迹更新失败: orderId={}, orderEntryId={}, error={}",
                orderId, orderEntryId, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 处理物流单号变更
     */
    private void processLogisticsMailNoChange(LogisticsContextRecord logisticsContext) {
        OrderContextRecord orderContext = logisticsContext.orderContextRecord();
        String orderId = orderContext.getAlibabaOrderIdStr();
        String logisticsId = logisticsContext.logisticsId();

        log.info("处理物流单号变更: orderId={}, logisticsId={}", orderId, logisticsId);

        try {
            // 1. 提取新的物流单号信息
            String newTrackingNo = extractNewTrackingNumber(logisticsContext);
            String oldTrackingNo = extractOldTrackingNumber(logisticsContext);

            if (newTrackingNo == null) {
                log.warn("无法提取新的物流单号，跳过处理: orderId={}", orderId);
                return;
            }

            log.info("物流单号变更: orderId={}, oldTrackingNo={}, newTrackingNo={}",
                orderId, oldTrackingNo, newTrackingNo);

            // 2. 更新供应商订单中的物流单号
            updateSupplierOrderTrackingNumber(orderContext, oldTrackingNo, newTrackingNo);

            // 3. 更新订单项中的物流单号
            updateOrderItemTrackingNumber(orderContext, oldTrackingNo, newTrackingNo);

            // 4. 同步物流单号到 WMS 系统
            syncTrackingNumberToWms(orderContext, newTrackingNo);

            log.info("物流单号变更完成: orderId={}, newTrackingNo={}", orderId, newTrackingNo);

        } catch (Exception e) {
            log.error("处理物流单号变更失败: orderId={}, logisticsId={}, error={}",
                orderId, logisticsId, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 更新订单物流状态
     */
    private void updateOrderLogisticsStatus(OrderContextRecord orderContext, LogisticsContextRecord logisticsContext) {
        try {
            String orderId = orderContext.getAlibabaOrderIdStr();
            TzOrderSupplier orderSupplier = orderContext.getTzOrderSupplierByOrderId(orderId);

            // 提取物流状态信息
            String logisticsStatus = extractLogisticsStatus(logisticsContext);
            String trackingInfo = extractTrackingInfo(logisticsContext);

            if (logisticsStatus != null) {
                log.info("更新订单物流状态: orderId={}, logisticsStatus={}", orderId, logisticsStatus);

                // 更新供应商订单的物流状态
//                orderSupplier.setPlatformLogisticsStatus(logisticsStatus);
//                orderSupplier.setGmtModified(LocalDateTime.now());

//                if (trackingInfo != null) {
//                    orderSupplier.setLogisticsInfo(trackingInfo);
//                }
            }

        } catch (Exception e) {
            log.error("更新订单物流状态失败: error={}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 更新订单项物流状态
     */
    private void updateOrderItemLogisticsStatus(OrderContextRecord orderContext, LogisticsContextRecord logisticsContext) {
        try {
            String orderId = logisticsContext.logisticsOrderId();
            String orderEntryId = logisticsContext.logisticsOrderEntryId();

            // 获取相关的订单项
            List<TzOrderItem> orderItems;
            if (orderEntryId != null) {
                // 如果有订单项ID，只更新特定订单项
                orderItems = orderContext.tzOrderItems().stream()
                    .filter(item -> Objects.equals(item.getPlatformItemId(), orderEntryId) ||
                        Objects.equals(item.getExternalItemId(), orderEntryId))
                    .toList();
            } else {
                // 否则更新该订单的所有订单项
                orderItems = orderContext.tzOrderItems().stream()
                    .filter(item -> Objects.equals(item.getPlatformOrderId(), orderId))
                    .toList();
            }

            if (orderItems.isEmpty()) {
                log.warn("未找到需要更新的订单项: orderId={}, orderEntryId={}", orderId, orderEntryId);
                return;
            }

            // 提取物流状态
            String logisticsStatusCode = extractLogisticsStatus(logisticsContext);
            TzOrderItemLogisticsStatusEnum logisticsStatus = null;
            if (logisticsStatusCode != null) {
                try {
                    int statusCode = Integer.parseInt(logisticsStatusCode);
                    logisticsStatus = TzOrderItemLogisticsStatusEnum.getByCode(statusCode);
                } catch (NumberFormatException e) {
                    log.warn("物流状态码格式错误: {}", logisticsStatusCode);
                }
            }

            // 更新每个订单项的物流状态
            for (TzOrderItem orderItem : orderItems) {
                log.info("更新订单项物流状态: orderId={}, itemId={}, logisticsStatus={}",
                    orderId, orderItem.getPlatformItemId(), logisticsStatus);

                if (logisticsStatus != null) {
                    orderItem.setLogisticsStatus(logisticsStatus);
                }

                // 提取并设置物流轨迹信息
                String trackingInfo = extractTrackingInfo(logisticsContext);
                if (trackingInfo != null) {
                    orderItem.setLogisticsMetadata(trackingInfo);
                }

                // 更新修改时间
                orderItem.setGmtModified(LocalDateTime.now());
            }

        } catch (Exception e) {
            log.error("更新订单项物流状态失败: error={}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 更新 WMS 采购订单物流状态
     */
    private void updateWmsPurchaseOrderLogisticsStatus(OrderContextRecord orderContext, @SuppressWarnings("unused") LogisticsContextRecord logisticsContext) {
        try {
            // TODO: 实现 WMS 采购订单物流状态更新逻辑
            // 这里需要调用 WMS 系统的接口来更新物流状态
            log.info("更新 WMS 采购订单物流状态: orderId={}", orderContext.getAlibabaOrderIdStr());

        } catch (Exception e) {
            log.error("更新 WMS 采购订单物流状态失败: error={}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 同步物流信息到供应商订单
     */
    private void syncLogisticsToSupplierOrder(OrderContextRecord orderContext, @SuppressWarnings("unused") LogisticsContextRecord logisticsContext) {
        try {
            // TODO: 实现供应商订单物流信息同步逻辑
            log.info("同步物流信息到供应商订单: orderId={}", orderContext.getAlibabaOrderIdStr());

        } catch (Exception e) {
            log.error("同步物流信息到供应商订单失败: error={}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 更新供应商订单物流单号
     */
    private void updateSupplierOrderTrackingNumber(OrderContextRecord orderContext, String oldTrackingNo, String newTrackingNo) {
        try {
            String orderId = orderContext.getAlibabaOrderIdStr();
            TzOrderSupplier orderSupplier = orderContext.getTzOrderSupplierByOrderId(orderId);

            log.info("更新供应商订单物流单号: orderId={}, oldTrackingNo={}, newTrackingNo={}",
                orderId, oldTrackingNo, newTrackingNo);

            orderSupplier.setPlatformTrackingNo(newTrackingNo);

        } catch (Exception e) {
            log.error("更新供应商订单物流单号失败: error={}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 更新订单项物流单号
     */
    private void updateOrderItemTrackingNumber(OrderContextRecord orderContext, String oldTrackingNo, String newTrackingNo) {
        try {
            String orderId = orderContext.getAlibabaOrderIdStr();

            List<TzOrderItem> orderItems = orderContext.tzOrderItems().stream()
                .filter(item -> Objects.equals(item.getPlatformOrderId(), orderId))
                .filter(item -> oldTrackingNo == null || Objects.equals(item.getLogisticsNo(), oldTrackingNo))
                .toList();

            for (TzOrderItem orderItem : orderItems) {
                log.info("更新订单项物流单号: orderId={}, itemId={}, newTrackingNo={}",
                    orderId, orderItem.getPlatformItemId(), newTrackingNo);

                orderItem.setLogisticsNo(newTrackingNo);
                orderItem.setGmtModified(LocalDateTime.now());
            }

        } catch (Exception e) {
            log.error("更新订单项物流单号失败: error={}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 同步物流单号到 WMS 系统
     */
    private void syncTrackingNumberToWms(OrderContextRecord orderContext, String newTrackingNo) {
        try {
            // TODO: 实现 WMS 系统物流单号同步逻辑
            log.info("同步物流单号到 WMS 系统: orderId={}, trackingNo={}",
                orderContext.getAlibabaOrderIdStr(), newTrackingNo);

        } catch (Exception e) {
            log.error("同步物流单号到 WMS 系统失败: error={}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 确定消息类型
     */
    private LogisticsMessageTypeEnums determineMessageType(@SuppressWarnings("unused") LogisticsMessage logisticsMessage) {
        // TODO: 根据实际的消息结构来确定消息类型
        // 这里可能需要分析 logisticsMessage 的内容来确定具体的消息类型
        return LogisticsMessageTypeEnums.LOGISTICS_BUYER_VIEW_TRACE;
    }

    /**
     * 提取物流状态
     */
    private String extractLogisticsStatus(LogisticsContextRecord logisticsContext) {
        try {
//            Object orderLogsItem = logisticsContext.orderLogsItem();
//            if (orderLogsItem instanceof Map) {
//                @SuppressWarnings("unchecked")
//                Map<String, Object> itemMap = (Map<String, Object>) orderLogsItem;
//                Object status = itemMap.get("logisticsStatus");
//                return status != null ? status.toString() : null;
//            }
            return null;
        } catch (Exception e) {
            log.warn("提取物流状态失败: error={}", e.getMessage());
            return null;
        }
    }

    /**
     * 提取物流轨迹信息
     */
    private String extractTrackingInfo(LogisticsContextRecord logisticsContext) {
        try {
//            logisticsContext.logisticsMessage().
//                Object trackingInfo = orderLogsItem
//                return trackingInfo != null ? trackingInfo.toString() : null;
            return null;
        } catch (Exception e) {
            log.warn("提取物流轨迹信息失败: error={}", e.getMessage());
            return null;
        }
    }

    /**
     * 提取新的物流单号
     */
    private String extractNewTrackingNumber(LogisticsContextRecord logisticsContext) {
        try {
//            Object orderLogsItem = logisticsContext.orderLogsItem();
//            if (orderLogsItem instanceof Map) {
//                @SuppressWarnings("unchecked")
//                Map<String, Object> itemMap = (Map<String, Object>) orderLogsItem;
//                Object newTrackingNo = itemMap.get("newTrackingNo");
//                return newTrackingNo != null ? newTrackingNo.toString() : null;
//            }
            return null;
        } catch (Exception e) {
            log.warn("提取新物流单号失败: error={}", e.getMessage());
            return null;
        }
    }

    /**
     * 提取旧的物流单号
     */
    private String extractOldTrackingNumber(LogisticsContextRecord logisticsContext) {
        try {
//            Object orderLogsItem = logisticsContext.orderLogsItem();
//            if (orderLogsItem instanceof Map) {
//                @SuppressWarnings("unchecked")
//                Map<String, Object> itemMap = (Map<String, Object>) orderLogsItem;
//                Object oldTrackingNo = itemMap.get("oldTrackingNo");
//                return oldTrackingNo != null ? oldTrackingNo.toString() : null;
//            }
            return null;
        } catch (Exception e) {
            log.warn("提取旧物流单号失败: error={}", e.getMessage());
            return null;
        }
    }

    @Override
    public List<LogisticsMessageTypeEnums> supports() {
        return List.of(
            LogisticsMessageTypeEnums.LOGISTICS_BUYER_VIEW_TRACE,
            LogisticsMessageTypeEnums.LOGISTICS_MAIL_NO_CHANGE
        );
    }
}
