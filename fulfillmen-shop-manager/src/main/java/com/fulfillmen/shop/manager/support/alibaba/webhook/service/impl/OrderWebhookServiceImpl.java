/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl;

import com.fulfillmen.shop.manager.support.alibaba.IOrderManager;
import com.fulfillmen.shop.manager.support.alibaba.webhook.event.OrderWebhookEvent;
import com.fulfillmen.shop.manager.support.alibaba.webhook.handler.LogisticsContextRecord;
import com.fulfillmen.shop.manager.support.alibaba.webhook.handler.OrderContextRecord;
import com.fulfillmen.shop.manager.support.alibaba.webhook.processor.OrderEventProcessor;
import com.fulfillmen.shop.manager.support.alibaba.webhook.processor.OrderEventProcessorRegistry;
import com.fulfillmen.shop.manager.support.alibaba.webhook.processor.impl.LogisticsProcessor;
import com.fulfillmen.shop.manager.support.alibaba.webhook.service.OrderDataSyncService;
import com.fulfillmen.shop.manager.support.alibaba.webhook.service.OrderWebhookService;
import com.fulfillmen.shop.manager.support.wms.IWmsManager;
import com.fulfillmen.starter.core.exception.BusinessException;
import com.fulfillmen.support.alibaba.api.request.order.OrderDetailRequestRecord;
import com.fulfillmen.support.alibaba.api.response.order.OrderDetailResponse;
import com.fulfillmen.support.alibaba.enums.LogisticsMessageTypeEnums;
import com.fulfillmen.support.alibaba.enums.OrderMessageTypeEnums;
import com.fulfillmen.support.alibaba.enums.OrderStatusEnums;
import com.fulfillmen.support.alibaba.webhook.MessageEvent;
import com.fulfillmen.support.alibaba.webhook.data.LogisticsMessage;
import com.fulfillmen.support.alibaba.webhook.data.LogisticsMessage.OrderLogItem;
import com.fulfillmen.support.alibaba.webhook.data.OrderMessage;
import com.fulfillmen.support.wms.dto.request.PurchaseOrderDetailReq;
import com.fulfillmen.support.wms.dto.response.WmsPurchaseOrderDetailsRes;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * 订单Webhook业务处理服务实现 - 策略模式重构版
 *
 * <pre>
 * 重构要点：
 * 1. 使用策略模式，将具体的业务处理逻辑下沉到独立的处理器中
 * 2. 本类专注于数据获取、同步和路由，不再包含具体的业务逻辑
 * 3. 通过 OrderEventProcessorRegistry 动态分发消息到对应的处理器
 * 4. 保持统一的异常处理和日志记录
 * 5. 简化了类的职责，提高了可维护性和可扩展性
 * </pre>
 *
 * <AUTHOR>
 * @date 2025/8/8 (重构版本)
 * @description 订单webhook业务处理服务实现，使用策略模式
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OrderWebhookServiceImpl implements OrderWebhookService {

    private final OrderDataSyncService orderDataSyncService;
    private final IOrderManager orderManager;
    private final IWmsManager wmsManager;
    private final ApplicationEventPublisher eventPublisher;
    //    private final ThreadPoolTaskExecutor threadPoolTaskExecutor;
    private final Executor virtualThreadExecutor;
    private final OrderEventProcessorRegistry processorRegistry;

    @Override
    public void processOrderWebhook(OrderMessage orderMessage, MessageEvent<OrderMessage> messageEvent,
        OrderMessageTypeEnums messageType) {
        String orderId = String.valueOf(orderMessage.getOrderId());
        String msgId = messageEvent.getMsgId();

        try {
            log.info("开始处理订单webhook消息: orderId={}, msgId={}, messageType={}, currentStatus={}", orderId, msgId, messageType.getMessageType(), orderMessage.getCurrentStatus());

            // 校验订单状态是否符合
            OrderStatusEnums orderStatusEnums = OrderStatusEnums.fromCode(orderMessage.getCurrentStatus());
            if (orderStatusEnums == null) {
                log.warn("暂不支持的处理流程里: [{}]", orderMessage.getCurrentStatus());
                return;
            }

            // 1. 异步获取订单数据
            CompletableFuture<OrderDetailResponse.OrderDetail> orderDetailFuture = getOrderDetailAsync(orderMessage.getOrderId());
            CompletableFuture<List<WmsPurchaseOrderDetailsRes>> wmsOrderDetailsFuture = getWmsOrderDetailsAsync(orderId);

            // 等待全部任务完成
            CompletableFuture.allOf(orderDetailFuture, wmsOrderDetailsFuture).join();

            // 2. 等待数据获取完成
            OrderDetailResponse.OrderDetail orderDetail = orderDetailFuture.join();
            List<WmsPurchaseOrderDetailsRes> wmsOrderDetails = wmsOrderDetailsFuture.join();

            log.info("订单数据获取完成: orderId={}, alibabaOrderDetail={}, wmsOrderDetails={}",
                orderId, orderDetail != null, wmsOrderDetails != null ? wmsOrderDetails.size() : 0);

            // 3. 验证数据有效性
            validateOrderData(orderDetail, wmsOrderDetails, orderId);

            // 4. 检查数据完整性
            OrderDataSyncService.OrderDataIntegrityResult integrityResult = orderDataSyncService.checkOrderDataIntegrity(orderDetail);

            // 5. 如果 wms 为 null，并且供应商订单存在，则通过供应商订单获取 wms 采购订单号
            // 这步骤属于补救措施，再次确认数据是否正确
            if (Objects.isNull(wmsOrderDetails) && integrityResult.hasSupplierOrders()) {
                var currentOrderSupplier = integrityResult.existingSupplierOrders().stream()
                    .filter(orderSupplier -> Objects.equals(orderSupplier.getPlatformOrderId(), orderId))
                    .findFirst().orElse(null);
                // 通过供应商订单获取 wms 采购订单号
                wmsOrderDetails = getWmsOrderDetailsBySupplierOrderAsync(currentOrderSupplier).join();
                if (!CollectionUtils.isEmpty(wmsOrderDetails)) {
                    log.info("通过供应商订单获取 wms 采购订单成功: [{}]", wmsOrderDetails.size());
                    WmsPurchaseOrderDetailsRes wmsOrderDetailsFirst = wmsOrderDetails.getFirst();
                    if (Objects.isNull(wmsOrderDetailsFirst.getOrderId()) || wmsOrderDetailsFirst.getOrderId() <= 0L) {
                        wmsOrderDetails.getFirst().setOrderId(Long.valueOf(orderId));
                    }
                } else {
                    log.warn("请检查一下 wms 采购订单是否存在。");
                    throw new BusinessException("wms 采购订单不存在, 无法进行后续操作。");
                }
            }

            log.info("订单数据完整性检查结果: orderId={}, isComplete={}, isNewVersion={}, missing={}", orderId, integrityResult.isDataComplete(), integrityResult.isNewVersionData(),
                integrityResult.getMissingDataDescription());

            // 6. 同步和补齐数据
            OrderContextRecord orderContextRecord = orderDataSyncService.syncAndCompleteOrderData(orderId, integrityResult, orderDetail, wmsOrderDetails);

            // 7. 发布数据同步完成事件
            publishDataSyncCompletedEvent(orderMessage, messageEvent, messageType, orderContextRecord);

            // 8. 路由到具体的业务处理逻辑
            routeToBusinessLogic(orderMessage, messageEvent, messageType, orderContextRecord);

            log.info("订单webhook消息处理完成: orderId={}, msgId={}, messageType={}", orderId, msgId, messageType.getMessageType());

        } catch (Exception e) {
            log.error("订单webhook消息处理失败: orderId={}, msgId={}, messageType={}, error={}",
                orderId, msgId, messageType.getMessageType(), e.getMessage(), e);

            // 发布处理失败事件
            publishProcessingFailedEvent(orderMessage, messageEvent, messageType, e);
            throw e;
        }
    }

    @Override
    public void processLogisticsWebhook(LogisticsMessage logisticsMessage, MessageEvent<LogisticsMessage> messageEvent, LogisticsMessageTypeEnums messageType) {
        String logisticsId = logisticsMessage.getLogisticsId();
        String msgId = messageEvent.getMsgId();

        try {
            log.info("开始处理订单物流 webhook 消息: logisticsId={}, msgId={}, messageType={}, orderLogsItemsCount={}",
                logisticsId, msgId, messageType.getMessageType(),
                logisticsMessage.getOrderLogsItems() != null ? logisticsMessage.getOrderLogsItems().size() : 0);

            if (CollectionUtils.isEmpty(logisticsMessage.getOrderLogsItems())) {
                log.warn("物流消息中没有订单项信息，跳过处理: logisticsId={}, msgId={}", logisticsId, msgId);
                return;
            }

            // 处理每个订单项的物流信息
            for (var orderLogsItem : logisticsMessage.getOrderLogsItems()) {
                processOrderLogisticsItem(orderLogsItem, logisticsMessage, messageEvent, messageType);
            }

            log.info("订单物流 webhook 消息处理完成: logisticsId={}, msgId={}, messageType={}",
                logisticsId, msgId, messageType.getMessageType());

        } catch (Exception e) {
            log.error("订单物流 webhook 消息处理失败: logisticsId={}, msgId={}, messageType={}, error={}", logisticsId, msgId, messageType.getMessageType(), e.getMessage(), e);
            publishProcessingFailedEvent(logisticsMessage, messageEvent, messageType, e);

            if (e instanceof BusinessException) {
                throw e;
            }
            throw new BusinessException("订单物流 webhook 消息处理失败, 请检查日志。");
        }
    }

    /**
     * 处理单个订单物流项
     */
    private void processOrderLogisticsItem(LogisticsMessage.OrderLogItem orderLogsItem, LogisticsMessage logisticsMessage,
        MessageEvent<LogisticsMessage> messageEvent, LogisticsMessageTypeEnums messageType) {
        try {
            // 从 orderLogsItem 中提取订单ID（这里需要根据实际的数据结构调整）
            String orderId = extractOrderIdFromLogisticsItem(orderLogsItem);
            String orderEntryId = extractOrderEntryIdFromLogisticsItem(orderLogsItem);

            if (Objects.isNull(orderId)) {
                log.warn("无法从物流项中提取订单ID，跳过处理: item={}", orderLogsItem);
                return;
            }

            log.info("处理订单物流项: orderId={}, orderEntryId={}, logisticsId={}", orderId, orderEntryId, logisticsMessage.getLogisticsId());

            // 1. 异步获取订单相关数据
            CompletableFuture<OrderDetailResponse.OrderDetail> orderDetailFuture = getOrderDetailAsync(Long.valueOf(orderId));
            CompletableFuture<List<WmsPurchaseOrderDetailsRes>> wmsOrderDetailsFuture = getWmsOrderDetailsAsync(orderId);

            // 2. 等待数据获取完成
            CompletableFuture.allOf(orderDetailFuture, wmsOrderDetailsFuture).join();

            OrderDetailResponse.OrderDetail orderDetail = orderDetailFuture.join();
            List<WmsPurchaseOrderDetailsRes> wmsOrderDetails = wmsOrderDetailsFuture.join();

            log.info("物流相关订单数据获取完成: orderId={}, alibabaOrderDetail={}, wmsOrderDetails={}",
                orderId, orderDetail != null, wmsOrderDetails != null ? wmsOrderDetails.size() : 0);

            // 3. 验证数据有效性
            validateOrderDataForLogistics(orderDetail, orderId);

            // 4. 检查数据完整性
            OrderDataSyncService.OrderDataIntegrityResult integrityResult = orderDataSyncService.checkOrderDataIntegrity(orderDetail);

            // 5. 如果 wms 数据为空，尝试通过供应商订单获取
            if (Objects.isNull(wmsOrderDetails) && integrityResult.hasSupplierOrders()) {
                var currentOrderSupplier = integrityResult.existingSupplierOrders().stream()
                    .filter(orderSupplier -> Objects.equals(orderSupplier.getPlatformOrderId(), orderId))
                    .findFirst().orElse(null);

                wmsOrderDetails = getWmsOrderDetailsBySupplierOrderAsync(currentOrderSupplier).join();
                if (!CollectionUtils.isEmpty(wmsOrderDetails)) {
                    log.info("通过供应商订单获取 wms 采购订单成功: [{}]", wmsOrderDetails.size());
                    WmsPurchaseOrderDetailsRes wmsOrderDetailsFirst = wmsOrderDetails.getFirst();
                    if (Objects.isNull(wmsOrderDetailsFirst.getOrderId()) || wmsOrderDetailsFirst.getOrderId() <= 0L) {
                        wmsOrderDetailsFirst.setOrderId(Long.valueOf(orderId));
                    }
                }
            }

            // 6. 同步和补齐数据，构建上下文
            OrderContextRecord orderContextRecord = orderDataSyncService.syncAndCompleteOrderData(orderId, integrityResult, orderDetail, wmsOrderDetails);

            // 7. 创建物流上下文记录，包含物流相关信息
            LogisticsContextRecord logisticsContextRecord = createLogisticsContextRecord(orderContextRecord, logisticsMessage, orderLogsItem);

            // 8. 路由到具体的物流处理器
            routeToLogisticsProcessor(logisticsMessage, messageEvent, messageType, logisticsContextRecord);

            log.info("订单物流项处理完成: orderId={}, orderEntryId={}", orderId, orderEntryId);

        } catch (Exception e) {
            log.error("处理订单物流项失败: item={}, error={}", orderLogsItem, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 从物流项中提取订单ID
     */
    private String extractOrderIdFromLogisticsItem(LogisticsMessage.OrderLogItem orderLogsItem) {
        try {
            Long orderId = orderLogsItem.getOrderId();
            return orderId != null ? orderId.toString() : null;
        } catch (Exception e) {
            log.warn("提取订单ID失败: item={}, error={}", orderLogsItem, e.getMessage());
            return null;
        }
    }

    /**
     * 从物流项中提取订单项ID
     */
    private String extractOrderEntryIdFromLogisticsItem(LogisticsMessage.OrderLogItem orderLogsItem) {
        try {
            Long orderEntryId = orderLogsItem.getOrderEntryId();
            return orderEntryId != null ? orderEntryId.toString() : null;
        } catch (Exception e) {
            log.warn("提取订单项ID失败: item={}, error={}", orderLogsItem, e.getMessage());
            return null;
        }
    }

    /**
     * 验证物流相关订单数据有效性
     */
    private void validateOrderDataForLogistics(OrderDetailResponse.OrderDetail orderDetail, String orderId) {
        if (orderDetail == null) {
            throw new IllegalArgumentException("1688订单详情为空，无法处理物流信息: orderId=" + orderId);
        }
        // 可以添加更多的物流相关验证逻辑
    }

    /**
     * 创建物流上下文记录
     */
    private LogisticsContextRecord createLogisticsContextRecord(OrderContextRecord orderContextRecord,
        LogisticsMessage logisticsMessage, OrderLogItem orderLogsItem) {
        // 创建包含物流信息的上下文记录
        return LogisticsContextRecord.builder()
            .orderContextRecord(orderContextRecord)
            .logisticsMessage(logisticsMessage)
            .orderLogsItem(orderLogsItem)
            .logisticsId(logisticsMessage.getLogisticsId())
            .build();
    }

    /**
     * 路由到物流处理器
     */
    private void routeToLogisticsProcessor(LogisticsMessage logisticsMessage,
        @SuppressWarnings("unused") MessageEvent<LogisticsMessage> messageEvent,
        LogisticsMessageTypeEnums messageType,
        LogisticsContextRecord logisticsContextRecord) {
        log.info("路由到物流处理器: logisticsId={}, messageType={}",
            logisticsMessage.getLogisticsId(), messageType);

        // 从注册表中获取对应的物流处理器
        var processor = processorRegistry.getLogisticsProcessor(messageType);
        if (processor.isEmpty()) {
            log.warn("未找到支持的物流消息处理器: messageType={}, logisticsId={}",
                messageType, logisticsMessage.getLogisticsId());
            return;
        }

        // 委托给具体的处理器执行业务逻辑
        try {
            var logisticsProcessor = processor.get();
            if (logisticsProcessor instanceof LogisticsProcessor actLogisticsProcessor) {
                actLogisticsProcessor.process(logisticsContextRecord);
            }

            log.info("物流消息处理完成: logisticsId={}, messageType={}",
                logisticsMessage.getLogisticsId(), messageType);
        } catch (BusinessException e) {
            log.error("物流消息处理失败: logisticsId={}, messageType={}, error={}",
                logisticsMessage.getLogisticsId(), messageType, e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            log.error("物流消息处理失败: logisticsId={}, messageType={}, error={}",
                logisticsMessage.getLogisticsId(), messageType, e.getMessage(), e);
            throw new BusinessException("物流消息处理失败: " + e.getMessage(), e);
        }
    }

    /**
     * 根据当前的 webhook order 订单 ID，获取到供应商订单，就能获取到 wms 订单
     *
     * <pre>
     * 补救措施
     * </pre>
     *
     * @param orderSupplier 供应商订单
     * @return List<WmsPurchaseOrderDetailsRes>
     */
    private CompletableFuture<List<WmsPurchaseOrderDetailsRes>> getWmsOrderDetailsBySupplierOrderAsync(
        Object orderSupplier) {
        return CompletableFuture.supplyAsync(() -> {
            if (Objects.isNull(orderSupplier)) {
                log.warn("供应商订单不存在");
                return null;
            }
            return null;
        }, virtualThreadExecutor);
    }

    /**
     * 异步获取1688订单详情
     */
    private CompletableFuture<OrderDetailResponse.OrderDetail> getOrderDetailAsync(Long orderId) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                OrderDetailRequestRecord request = OrderDetailRequestRecord.builder()
                    .webSite("1688")
                    .orderId(orderId)
                    .build();
                return orderManager.getOrderDetail(request);
            } catch (Exception e) {
                log.error("获取1688订单详情失败: orderId={}", orderId, e);
                throw new RuntimeException("获取1688订单详情失败", e);
            }
        }, virtualThreadExecutor);
    }

    /**
     * 异步获取WMS订单详情
     */
    private CompletableFuture<List<WmsPurchaseOrderDetailsRes>> getWmsOrderDetailsAsync(String orderId) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                PurchaseOrderDetailReq request = PurchaseOrderDetailReq.builder()
                    .orderId(orderId)
                    .build();
                return wmsManager.queryOrderDetail(request);
            } catch (Exception e) {
                log.error("获取WMS订单详情失败: orderId={}", orderId, e);
                return null;
            }
        }, virtualThreadExecutor);
    }

    /**
     * 验证订单数据有效性
     */
    private void validateOrderData(OrderDetailResponse.OrderDetail orderDetail, List<WmsPurchaseOrderDetailsRes> wmsOrderDetails, String orderId) {
        if (orderDetail == null) {
            throw new IllegalArgumentException("1688订单详情为空: orderId=" + orderId);
        }

        if (CollectionUtils.isEmpty(wmsOrderDetails)) {
            log.warn("WMS订单详情为空，可能是旧版数据: orderId={}", orderId);
            // 注意：这里不抛异常，因为旧版数据可能没有WMS记录，不存在没有采购记录。如果没有那么这个订单有问题。
            // throw new IllegalArgumentException("WMS订单详情为空: orderId=" + orderId);
        }
    }

    /**
     * 路由到具体的业务处理逻辑
     * <p>
     * 使用策略模式，根据消息类型动态分发到对应的处理器。 这种设计使得添加新的事件处理逻辑变得非常简单，无需修改此方法。
     * </p>
     */
    private void routeToBusinessLogic(OrderMessage orderMessage, MessageEvent<OrderMessage> messageEvent,
        OrderMessageTypeEnums messageType, OrderContextRecord orderContextRecord) {
        log.info("路由到具体的业务处理逻辑: orderId={}, messageType={}", orderMessage.getOrderId(), messageType);

        // 对于退款消息，需要特殊的前置条件检查
        if (isRefundMessage(messageType)) {
            if (!isRefundActionSupported(orderMessage.getRefundAction())) {
                log.warn("不支持的退款操作类型，跳过处理。orderId={}, refundAction={}",
                    orderMessage.getOrderId(), orderMessage.getRefundAction());
                return;
            }
        }

        // 从注册表中获取对应的处理器
        Optional<OrderEventProcessor<OrderMessageTypeEnums, OrderContextRecord>> processor = processorRegistry.getOrderProcessor(messageType);
        if (processor.isEmpty()) {
            log.warn("未找到支持的订单消息处理器: messageType={}, orderId={}", messageType, orderMessage.getOrderId());
            return;
        }

        // 委托给具体的处理器执行业务逻辑
        try {
            processor.get().process(orderContextRecord);
            log.info("订单消息处理完成: orderId={}, messageType={}", orderMessage.getOrderId(), messageType);
        } catch (Exception e) {
            log.error("订单消息处理失败: orderId={}, messageType={}, error={}",
                orderMessage.getOrderId(), messageType, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 检查是否为退款消息类型
     */
    private boolean isRefundMessage(OrderMessageTypeEnums messageType) {
        return messageType == OrderMessageTypeEnums.ORDER_BUYER_VIEW_ORDER_BUYER_REFUND_IN_SALES ||
            messageType == OrderMessageTypeEnums.ORDER_BUYER_VIEW_ORDER_REFUND_AFTER_SALES;
    }

    /**
     * 检查退款操作类型是否被支持
     */
    private boolean isRefundActionSupported(String refundAction) {
        if (refundAction == null || refundAction.trim().isEmpty()) {
            return false;
        }
        // 使用与 OrderRefundProcessor 中相同的支持列表
        List<String> supportedActions = List.of(
            "SELLER_AGREE_REFUND",
            "SYSTEM_AGREE_REFUND_PROTOCOL",
            "SYSTEM_AGREE_REFUND",
            "SELLER_AGREE_REFUND_PROCOTOL");
        return supportedActions.contains(refundAction);
    }

    /**
     * 发布数据同步完成事件
     */
    @SuppressWarnings("unused")
    private void publishDataSyncCompletedEvent(OrderMessage orderMessage,
        MessageEvent<OrderMessage> messageEvent,
        OrderMessageTypeEnums messageType,
        OrderContextRecord orderContextRecord) {
        OrderWebhookEvent event = OrderWebhookEvent.createDataSyncCompletedEvent(this, orderMessage, messageEvent,
            messageType, orderContextRecord);
        eventPublisher.publishEvent(event);
    }

    /**
     * 发布处理失败事件
     */
    @SuppressWarnings("unused")
    private void publishProcessingFailedEvent(OrderMessage orderMessage,
        MessageEvent<OrderMessage> messageEvent,
        OrderMessageTypeEnums messageType,
        Exception error) {
        // TODO: 实现处理失败事件的发布
        log.error("发布订单处理失败事件: orderId={}, error={}", orderMessage.getOrderId(), error.getMessage());
    }

    /**
     * 发布处理失败事件
     */
    @SuppressWarnings("unused")
    private void publishProcessingFailedEvent(LogisticsMessage logisticsMessage,
        MessageEvent<LogisticsMessage> messageEvent,
        LogisticsMessageTypeEnums messageType,
        Exception error) {
        // TODO: 实现处理失败事件的发布
        log.error("发布订单处理失败事件: logisticsMessage={}, error={}", logisticsMessage, error.getMessage());
    }
}
