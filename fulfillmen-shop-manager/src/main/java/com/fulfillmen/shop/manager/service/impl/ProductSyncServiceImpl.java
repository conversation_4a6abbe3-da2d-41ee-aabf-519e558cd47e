/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fulfillmen.shop.common.enums.FulfillmenErrorCodeEnum;
import com.fulfillmen.shop.common.exception.BusinessExceptionI18n;
import com.fulfillmen.shop.common.util.MetaInfoHashUtils;
import com.fulfillmen.shop.dao.mapper.TzProductSpuMapper;
import com.fulfillmen.shop.domain.convert.TzProductMapping;
import com.fulfillmen.shop.domain.dto.TzProductDTO;
import com.fulfillmen.shop.domain.dto.product.alibaba.AlibabaProductDetailDTO;
import com.fulfillmen.shop.domain.entity.PdcProductMapping;
import com.fulfillmen.shop.domain.entity.TzProductSku;
import com.fulfillmen.shop.domain.entity.TzProductSpu;
import com.fulfillmen.shop.domain.entity.enums.PdcProductMappingSyncStatusEnum;
import com.fulfillmen.shop.domain.entity.enums.PlatformCodeEnum;
import com.fulfillmen.shop.manager.core.repository.PdcProductMappingRepository;
import com.fulfillmen.shop.manager.core.repository.TzProductSkuRepository;
import com.fulfillmen.shop.manager.product.service.IProductStrategyService;
import com.fulfillmen.shop.manager.service.IProductSyncService;
import com.fulfillmen.shop.manager.service.dto.BatchSyncResult;
import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 产品同步服务实现 - 重构为门面模式
 *
 * <pre>
 * 架构优化：
 * 1. 门面模式：统一外部接口，隐藏内部复杂性
 * 2. 策略模式：根据配置选择不同的同步策略
 * 3. 职责分离：数据访问由Repository负责，同步逻辑由Strategy负责
 * 4. 扩展性：支持多平台接入和多种同步策略
 * </pre>
 *
 * <AUTHOR>
 * @date 2025/06/19
 * @since 2.0.0
 */
@Slf4j
@Service
@Primary
@RequiredArgsConstructor
public class ProductSyncServiceImpl implements IProductSyncService {

    private final PdcProductMappingRepository pdcProductMappingRepository;
    private final TzProductSkuRepository tzProductSkuRepository;
    private final TzProductSpuMapper tzProductSpuMapper;
    private final IProductStrategyService productStrategyService;
    //    private final ThreadPoolTaskExecutor threadPoolTaskExecutor;
    private final Executor virtualThreadExecutor;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TzProductDTO syncProductByPlatformId(String platformProductId) {
        log.debug("开始同步产品数据，platformProductId: {}", platformProductId);

        try {
            // 1. 检查SPU是否已存在（基于平台产品ID 或 PdcProductMapping ID）
            TzProductSpu existingSpu = getExistingSpuByPlatformId(platformProductId);
            Long productId = Long.valueOf(platformProductId);
            CompletableFuture<AlibabaProductDetailDTO> productDetailDTOCompletableFuture = CompletableFuture
                .supplyAsync(() -> this.pdcProductMappingRepository.getProductDetailWithCache(productId, false),
                    virtualThreadExecutor);
            if (existingSpu != null) {
                log.debug("SPU已存在（基于平台产品ID），直接返回，spuId: {}", existingSpu.getId());
                // 获取现有的SKU列表
                List<TzProductSku> existingSkuList = getSkuListBySpuId(existingSpu.getId());
                // 如果不存在，则创建一个
                if (CollectionUtil.isEmpty(existingSkuList)) {
                    log.warn("SKU列表为空，platformProductId: {}", platformProductId);
                    // 单品，创建一个默认SKU
                    TzProductSku defaultSku = TzProductMapping.INSTANCE.toSingleItemSku(productDetailDTOCompletableFuture.get(), existingSpu.getId());
                    if (defaultSku != null) {
                        // 确保SKU图片URL不为空
                        if (existingSpu.getWhiteImage() != null) {
                            defaultSku.setImage(existingSpu.getWhiteImage());
                        } else if (existingSpu.getMainImage() != null) {
                            defaultSku.setImage(existingSpu.getMainImage());
                        }

                        tzProductSkuRepository.save(defaultSku);
                        existingSkuList.add(defaultSku);
                        log.debug("单品默认SKU创建成功，skuId: {}", defaultSku.getId());
                    }
                }
                // 转换为DTO并应用策略处理
                TzProductDTO productDTO = TzProductMapping.INSTANCE.toTzProductDTO(existingSpu, existingSkuList, productDetailDTOCompletableFuture.get());
                return applyProductStrategies(productDTO, null, null, platformProductId);
            }
            AlibabaProductDetailDTO productDetail = productDetailDTOCompletableFuture.get();
            // 2. 从PdcProductMapping获取产品详情
            if (productDetail == null) {
                log.warn("未找到产品详情，platformProductId: {}", platformProductId);
                return null;
            }

            // 3. 使用 Mapstruct 创建SPU
            TzProductSpu spu = TzProductMapping.INSTANCE.toTzProductSpu(productDetail);

            try {
                tzProductSpuMapper.insert(spu);
                log.debug("SPU创建成功，spuId: {}", spu.getId());
            } catch (Exception e) {
                log.error("SPU创建失败，platformProductId: {}", platformProductId, e);
                throw BusinessExceptionI18n.of(FulfillmenErrorCodeEnum.PRODUCT_DETAIL_GET_FAILED);
            }

            // 5. 创建SKU
            List<TzProductSku> skuList = new ArrayList<>();
            if (!productDetail.isSingleItem() && CollectionUtil.isNotEmpty(productDetail.getProductSkuList())) {
                // 多规格产品，创建多个SKU
                skuList = TzProductMapping.INSTANCE.toTzProductSkuList(productDetail.getProductSkuList(), spu
                    .getId(), productDetail.getPlatformProductId(), spu.getMinOrderQuantity());
                // 确保SKU图片URL不为空，如果为空就给SPU的图片URL
                if (CollectionUtil.isNotEmpty(skuList)) {
                    skuList.forEach(sku -> {
                        if (sku.getImage() == null) {
                            // 优先使用白底图
                            if (spu.getWhiteImage() != null) {
                                sku.setImage(spu.getWhiteImage());
                            } else if (spu.getMainImage() != null) {
                                sku.setImage(spu.getMainImage());
                            }
                        }
                    });
                }

                if (CollectionUtil.isNotEmpty(skuList)) {
                    tzProductSkuRepository.batchInsertSkus(skuList);
                    log.debug("多规格SKU创建成功，数量: {}", skuList.size());
                }
            } else {
                // 单品，创建一个默认SKU
                TzProductSku defaultSku = TzProductMapping.INSTANCE.toSingleItemSku(productDetail, spu.getId());
                if (defaultSku != null) {
                    // 确保SKU图片URL不为空，如果为空就给SPU的图片URL
                    if (defaultSku.getImage() == null) {
                        // 优先使用白底图
                        if (spu.getWhiteImage() != null) {
                            defaultSku.setImage(spu.getWhiteImage());
                        } else if (spu.getMainImage() != null) {
                            defaultSku.setImage(spu.getMainImage());
                        }
                    }

                    tzProductSkuRepository.save(defaultSku);
                    skuList.add(defaultSku);
                    log.debug("单品默认SKU创建成功，skuId: {}", defaultSku.getId());
                }
            }

            // 7. 转换为DTO并应用策略处理
            TzProductDTO productDTO = TzProductMapping.INSTANCE.toTzProductDTO(spu, skuList, productDetail);
            return applyProductStrategies(productDTO, null, null, platformProductId);
        } catch (Exception e) {
            if (e instanceof BusinessExceptionI18n) {
                throw (BusinessExceptionI18n) e;
            }
            log.error("同步产品数据失败，platformProductId: {}", platformProductId, e);
            throw BusinessExceptionI18n.of(FulfillmenErrorCodeEnum.PRODUCT_DETAIL_GET_FAILED);
        }
    }

    @Override
    public List<TzProductSku> getSkuListBySpuId(Long spuId) {
        LambdaQueryWrapper<TzProductSku> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TzProductSku::getSpuId, spuId);
        return tzProductSkuRepository.list(queryWrapper);
    }

    @Override
    public boolean isSingleItem(String platformProductId) {
        try {
            Long productId = Long.valueOf(platformProductId);
            AlibabaProductDetailDTO productDetail = pdcProductMappingRepository
                .getProductDetailWithCache(productId, false);
            return productDetail != null && productDetail.isSingleItem();
        } catch (Exception e) {
            log.warn("检查产品是否为单品失败，platformProductId: {}", platformProductId, e);
            return false;
        }
    }

    @Override
    public TzProductSku getSkuByPlatformIds(String platformProductId, String platformSkuId) {
        LambdaQueryWrapper<TzProductSku> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TzProductSku::getPlatformProductId, platformProductId)
            .eq(TzProductSku::getPlatformSku, platformSkuId);
        return tzProductSkuRepository.getOne(queryWrapper);
    }

    @Override
    public BigDecimal getSingleItemPrice(String platformProductId) {
        try {
            Long productId = Long.valueOf(platformProductId);
            AlibabaProductDetailDTO productDetail = pdcProductMappingRepository
                .getProductDetailWithCache(productId, false);
            if (productDetail == null) {
                log.warn("获取单品价格失败，未找到产品详情，platformProductId: {}", platformProductId);
                return null;
            }

            // 使用 Mapstruct 中的价格提取逻辑
            return TzProductMapping.INSTANCE.extractDropShippingPriceFromProductDetail(productDetail);
        } catch (Exception e) {
            log.error("获取单品价格异常，platformProductId: {}", platformProductId, e);
            return null;
        }
    }

    @Override
    public TzProductSku getSingleItemDefaultSku(Long spuId) {
        LambdaQueryWrapper<TzProductSku> queryWrapper = new LambdaQueryWrapper<>();
        // 单品只有一个SKU
        queryWrapper.eq(TzProductSku::getSpuId, spuId).last("LIMIT 1");
        return tzProductSkuRepository.getOne(queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TzProductSku forceCreateDefaultSkuForSingleItem(Long spuId, String platformProductId) {
        log.warn("强制为单品创建默认SKU，spuId: {}, platformProductId: {}", spuId, platformProductId);

        try {
            // 1. 检查是否已存在默认SKU
            TzProductSku existingSku = getSingleItemDefaultSku(spuId);
            if (existingSku != null) {
                log.debug("单品默认SKU已存在，直接返回，skuId: {}", existingSku.getId());
                return existingSku;
            }

            // 2. 获取SPU信息
            TzProductSpu spu = tzProductSpuMapper.selectById(spuId);
            if (spu == null) {
                log.error("SPU不存在，无法创建默认SKU，spuId: {}", spuId);
                return null;
            }

            // 3. 从PdcProductMapping获取产品详情
            Long productId = Long.valueOf(platformProductId);
            AlibabaProductDetailDTO productDetail = pdcProductMappingRepository
                .getProductDetailWithCache(productId, false);
            if (productDetail == null) {
                log.warn("未找到产品详情，使用基础信息创建默认SKU，platformProductId: {}", platformProductId);
                // 使用基础信息创建默认SKU
                return createEmergencyDefaultSku(spuId, platformProductId, spu);
            }

            // 4. 使用 Mapstruct 创建默认SKU
            TzProductSku defaultSku = TzProductMapping.INSTANCE.toSingleItemSku(productDetail, spuId);
            if (defaultSku != null) {
                tzProductSkuRepository.save(defaultSku);
                log.info("强制创建单品默认SKU成功，spuId: {}, skuId: {}", spuId, defaultSku.getId());
                return defaultSku;
            } else {
                log.error("创建单品默认SKU失败，spuId: {}, platformProductId: {}", spuId, platformProductId);
                return null;
            }
        } catch (Exception e) {
            log.error("强制创建单品默认SKU异常，spuId: {}, platformProductId: {}", spuId, platformProductId, e);
            return null;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TzProductDTO resyncProductByPlatformId(String platformProductId, boolean forceUpdate) {
        log.debug("重新同步产品数据，platformProductId: {}, forceUpdate: {}", platformProductId, forceUpdate);

        try {
            Long productId = Long.valueOf(platformProductId);

            // 1. 检查现有SPU
            TzProductSpu existingSpu = getExistingSpuByPlatformId(platformProductId);

            // 注意：数据过期检查已经在上面的条件分支中处理了
            AlibabaProductDetailDTO alibabaProductDetailDTO = null;
            // 如果SPU存在且不强制更新，且数据未过期，则直接返回
            if (existingSpu != null && !forceUpdate) {
                // 检查数据是否过期
                Long pdcProductId = Optional.ofNullable(existingSpu).map(TzProductSpu::getPdcProductMappingId).orElse(null);
                boolean needResync = checkIfNeedResync(String.valueOf(Optional.ofNullable(pdcProductId).orElse(productId)));

                if (!needResync) {
                    log.debug("SPU已存在且数据未过期，直接返回，spuId: {}", existingSpu.getId());
                    // 获取现有的SKU列表
                    List<TzProductSku> existingSkuList = getSkuListBySpuId(existingSpu.getId());
                    alibabaProductDetailDTO = this.pdcProductMappingRepository.getProductDetailWithCache(Long.valueOf(existingSpu.getPdcPlatformProductId()), false);
                    // 转换为DTO并应用策略处理
                    TzProductDTO productDTO = TzProductMapping.INSTANCE.toTzProductDTO(existingSpu, existingSkuList, alibabaProductDetailDTO);
                    return applyProductStrategies(productDTO, null, null, platformProductId);

                } else {
                    log.info("SPU已存在但数据已过期，继续执行重新同步: platformProductId={}", platformProductId);
                    // 继续执行下面的重新同步逻辑
                }
            }

            // 2. 从PdcProductMapping获取最新产品详情
            // 强制刷新缓存
            AlibabaProductDetailDTO productDetail = Optional.ofNullable(existingSpu).map(productSpu -> pdcProductMappingRepository
                .getProductDetailWithCache(productSpu.getPdcProductMappingId(), true))
                .orElseGet(() -> pdcProductMappingRepository.getProductDetailWithCache(productId, true));
            if (productDetail == null) {
                log.error("未找到产品详情，无法重新同步，platformProductId: {}", platformProductId);
                return null;
            }
            // 3. SPU 不为空，更新现有SPU
            if (Objects.nonNull(existingSpu)) {
                log.info("开始重新同步现有产品，platformProductId: {}, spuId: {}, forceUpdate: {}",
                    platformProductId, existingSpu.getId(), forceUpdate);

                // 3. 更新现有SPU - 使用 LambdaUpdateWrapper 避免乐观锁问题
                log.debug("更新现有SPU数据，spuId: {}", existingSpu.getId());
                TzProductSpu updatedSpu = TzProductMapping.INSTANCE.toTzProductSpu(productDetail);

                // 使用 existingSpu 覆盖更新部分字段
                existingSpu.setTitle(updatedSpu.getTitle());
                existingSpu.setTitleTrans(updatedSpu.getTitleTrans());
                existingSpu.setDescription(updatedSpu.getDescription());
                existingSpu.setName(updatedSpu.getName());
                existingSpu.setNameTrans(updatedSpu.getNameTrans());
                existingSpu.setCategoryId(updatedSpu.getCategoryId());
                existingSpu.setCategoryName(updatedSpu.getCategoryName());
                existingSpu.setCategoryNameTrans(updatedSpu.getCategoryNameTrans());
                existingSpu.setMainImage(updatedSpu.getMainImage());
                existingSpu.setWhiteImage(updatedSpu.getWhiteImage());
                existingSpu.setImages(updatedSpu.getImages());
                existingSpu.setUnit(updatedSpu.getUnit());
                existingSpu.setUnitTrans(updatedSpu.getUnitTrans());
                existingSpu.setMainVideo(updatedSpu.getMainVideo());
                existingSpu.setDetailVideo(updatedSpu.getDetailVideo());
                existingSpu.setAttributeCpvs(updatedSpu.getAttributeCpvs());
                existingSpu.setShippingInfo(updatedSpu.getShippingInfo());
                existingSpu.setSkuShippingDetails(updatedSpu.getSkuShippingDetails());
                existingSpu.setMinOrderQuantity(updatedSpu.getMinOrderQuantity());
                existingSpu.setIsSingleItem(updatedSpu.getIsSingleItem());
                existingSpu.setPdcProductMappingId(updatedSpu.getPdcProductMappingId());
                existingSpu.setSourcePlatformSellerOpenId(updatedSpu.getSourcePlatformSellerOpenId());
                existingSpu.setSourcePlatformSellerName(updatedSpu.getSourcePlatformSellerName());

                int updateResult = tzProductSpuMapper.updateById(existingSpu);
                if (updateResult > 0) {
                    log.info("SPU数据更新成功，spuId: {}", existingSpu.getId());
                } else {
                    log.error("SPU数据更新失败，spuId: {}，可能是乐观锁冲突", existingSpu.getId());
                    return null;
                }

                // 4. 智能更新SKU（增量同步，保护现有数据）
                log.info("开始智能更新SKU数据，spuId: {}，使用增量同步策略保护现有订单数据", existingSpu.getId());
                List<TzProductSku> updatedSkuList = intelligentUpdateSkusForSpu(existingSpu.getId(), productDetail);

                if (updatedSkuList == null || updatedSkuList.isEmpty()) {
                    log.warn("SKU更新后列表为空，spuId: {}，这可能是一个问题", existingSpu.getId());
                }

                // 5. 转换为DTO返回
                log.info("产品重新同步完成，platformProductId: {}, spuId: {}, SKU数量: {}",
                    platformProductId, existingSpu.getId(), updatedSkuList != null ? updatedSkuList.size() : 0);
                // 6. 转换为DTO并应用策略处理
                TzProductDTO productDTO = TzProductMapping.INSTANCE.toTzProductDTO(existingSpu, updatedSkuList, productDetail);
                return applyProductStrategies(productDTO, null, null, platformProductId);
            } else {
                // 6. 创建新的SPU和SKU
                log.info("创建新的SPU，platformProductId: {}", platformProductId);
                return syncProductByPlatformId(platformProductId);
            }
        } catch (Exception e) {
            log.error("重新同步产品数据异常，platformProductId: {}, forceUpdate: {}", platformProductId, forceUpdate, e);
            return null;
        }
    }

    @Override
    public BatchSyncResult batchResyncProductsByPlatformIds(List<String> platformProductIds, boolean forceUpdate) {
        log.info("开始批量重新同步产品数据，数量: {}, forceUpdate: {}",
            platformProductIds != null ? platformProductIds.size() : 0, forceUpdate);

        LocalDateTime startTime = LocalDateTime.now();
        BatchSyncResult.BatchSyncResultBuilder resultBuilder = BatchSyncResult.builder()
            .startTime(startTime);

        // 参数验证
        if (platformProductIds == null || platformProductIds.isEmpty()) {
            log.warn("批量重新同步产品数据：输入列表为空");
            return resultBuilder
                .totalCount(0)
                .successCount(0)
                .failureCount(0)
                .skippedCount(0)
                .endTime(LocalDateTime.now())
                .executionTimeMs(Duration.between(startTime, LocalDateTime.now()).toMillis())
                .build();
        }

        // 去重处理
        List<String> uniqueProductIds = platformProductIds.stream()
            .distinct()
            .toList();

        int skippedCount = platformProductIds.size() - uniqueProductIds.size();
        List<String> skippedProductIds = new ArrayList<>();
        if (skippedCount > 0) {
            // 找出重复的ID
            Map<String, Long> idCountMap = platformProductIds.stream()
                .collect(Collectors.groupingBy(id -> id, Collectors.counting()));
            skippedProductIds = idCountMap.entrySet().stream()
                .filter(entry -> entry.getValue() > 1)
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());
            log.info("过滤重复产品ID，原始数量: {}, 去重后数量: {}, 重复数量: {}",
                platformProductIds.size(), uniqueProductIds.size(), skippedCount);
        }

        // 使用虚拟线程并发处理
        List<CompletableFuture<BatchSyncResult.FailedProduct>> futures = uniqueProductIds.stream()
            .map(productId -> CompletableFuture.supplyAsync(() -> {
                try {
                    TzProductDTO result = resyncProductByPlatformId(productId, forceUpdate);
                    if (result != null) {
                        return null; // 成功，返回null
                    } else {
                        return BatchSyncResult.FailedProduct.builder()
                            .platformProductId(productId)
                            .errorMessage("同步返回null，可能是产品不存在或同步失败")
                            .exceptionType("SyncFailure")
                            .failureTime(LocalDateTime.now())
                            .build();
                    }
                } catch (Exception e) {
                    log.error("批量同步单个产品失败: {}", productId, e);
                    return BatchSyncResult.FailedProduct.builder()
                        .platformProductId(productId)
                        .errorMessage(e.getMessage())
                        .exceptionType(e.getClass().getSimpleName())
                        .failureTime(LocalDateTime.now())
                        .build();
                }
            }, virtualThreadExecutor))
            .toList();

        // 等待所有任务完成并收集结果
        List<BatchSyncResult.FailedProduct> failedProducts = futures.stream()
            .map(CompletableFuture::join)
            .filter(Objects::nonNull)
            .collect(Collectors.toList());

        // 计算成功的产品（需要重新查询以获取完整数据）
        List<String> successProductIds = uniqueProductIds.stream()
            .filter(productId -> failedProducts.stream()
                .noneMatch(failed -> failed.getPlatformProductId().equals(productId)))
            .toList();

        List<TzProductDTO> successProducts = successProductIds.stream()
            .map(productId -> {
                try {
                    return getOrSyncProductByPlatformId(productId);
                } catch (Exception e) {
                    log.warn("获取成功同步的产品详情失败: {}", productId, e);
                    return null;
                }
            })
            .filter(Objects::nonNull)
            .collect(Collectors.toList());

        LocalDateTime endTime = LocalDateTime.now();
        long executionTimeMs = Duration.between(startTime, endTime).toMillis();

        BatchSyncResult result = resultBuilder
            .totalCount(uniqueProductIds.size())
            .successCount(successProducts.size())
            .failureCount(failedProducts.size())
            .skippedCount(skippedCount)
            .endTime(endTime)
            .executionTimeMs(executionTimeMs)
            .successProducts(successProducts)
            .failedProducts(failedProducts)
            .skippedProductIds(skippedProductIds)
            .build();

        log.info("批量重新同步产品数据完成，总数: {}, 成功: {}, 失败: {}, 跳过: {}, 耗时: {}ms, 成功率: {:.2f}%",
            result.getTotalCount(), result.getSuccessCount(), result.getFailureCount(),
            result.getSkippedCount(), result.getExecutionTimeMs(), result.getSuccessRate());

        return result;
    }

    /**
     * 检查SPU是否已存在（基于平台产品ID 或 PdcProductMappingId）
     *
     * @param platformProductId 平台产品ID
     * @return SPU实体，不存在返回null
     */
    private TzProductSpu getExistingSpuByPlatformId(String platformProductId) {
        log.debug("检查SPU是否已存在，platformProductId: {}", platformProductId);
        // 1.1 优先通过主键查询
        TzProductSpu productSpu = this.tzProductSpuMapper.selectById(platformProductId);
        if (Objects.nonNull(productSpu)) {
            return productSpu;
        }
        // 1.2 再次尝试 通过 pdcProductMappingId 获取 spuId
        return CompletableFuture.supplyAsync(() -> {
            LambdaQueryWrapper<TzProductSpu> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(TzProductSpu::getPlatformCode, PlatformCodeEnum.PLATFORM_CODE_1688)
                .eq(TzProductSpu::getPdcPlatformProductId, platformProductId);
            return tzProductSpuMapper.selectOne(queryWrapper);
        }, virtualThreadExecutor)
            // 1.3 再次尝试 通过 pdcProductMappingId 获取 spuId
            .thenApplyAsync(result -> {
                if (result != null) {
                    return result;
                }
                LambdaQueryWrapper<TzProductSpu> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(TzProductSpu::getIsPdcSync, PdcProductMappingSyncStatusEnum.SYNCED)
                    .eq(TzProductSpu::getPdcProductMappingId, platformProductId);
                return tzProductSpuMapper.selectOne(queryWrapper);
            }, virtualThreadExecutor)
            // 异常处理
            .handleAsync((result, ex) -> {
                if (ex != null) {
                    log.error("查询SPU失败，platformProductId: {}", platformProductId, ex);
                    return null;
                }
                return result;
            }, virtualThreadExecutor).join();
    }

    /**
     * 创建应急默认SKU（当产品详情不可用时）
     */
    private TzProductSku createEmergencyDefaultSku(Long spuId, String platformProductId, TzProductSpu spu) {
        try {
            long skuId = TzProductMapping.INSTANCE.generateId();
            TzProductSku emergencySku = TzProductSku.builder()
              .id(skuId)
              .spuId(spuId)
              .platformCode(PlatformCodeEnum.PLATFORM_CODE_1688)
              .platformProductId(platformProductId)
              .platformSku(platformProductId)
              .sku("SKU_" + skuId)
              .barcode(String.valueOf(skuId))
              .image(spu.getMainImage())
              .price(BigDecimal.ZERO)
              .dropShippingPrice(BigDecimal.ZERO)
              // 空规格
              .specs(List.of())
              .quantity(0)
              .build();

            this.tzProductSkuRepository.save(emergencySku);
            log.info("创建应急默认SKU成功，spuId: {}, skuId: {}", spuId, emergencySku.getId());
            return emergencySku;
        } catch (Exception e) {
            log.error("创建应急默认SKU失败，spuId: {}, platformProductId: {}", spuId, platformProductId, e);
            return null;
        }
    }

    /**
     * 为SPU创建SKU
     */
    private void createSkusForSpu(Long spuId, AlibabaProductDetailDTO productDetail) {
        // 获取SPU信息
        TzProductSpu spu = this.tzProductSpuMapper.selectById(spuId);

        if (!productDetail.isSingleItem() && CollectionUtil.isNotEmpty(productDetail.getProductSkuList())) {
            // 多规格产品
            List<TzProductSku> skuList = TzProductMapping.INSTANCE.toTzProductSkuList(productDetail
                .getProductSkuList(), spuId, productDetail.getPlatformProductId(),
                productDetail.getMinOrderQuantity());
            // 确保sku都有图片，如果缺少图片，则使用SPU的图片
            if (CollectionUtil.isNotEmpty(skuList)) {
                skuList.forEach(sku -> {
                    if (sku.getImage() == null && spu != null) {
                        // 优先使用白底图
                        if (spu.getWhiteImage() != null) {
                            sku.setImage(spu.getWhiteImage());
                            log.debug("SKU图片为空，使用SPU白底图: {}", sku.getImage());
                        } else if (spu.getMainImage() != null) {
                            sku.setImage(spu.getMainImage());
                            log.debug("SKU图片为空，使用SPU主图片: {}", sku.getImage());
                        }
                    }
                });

                this.tzProductSkuRepository.batchInsertSkus(skuList);
                log.debug("重新创建多规格SKU成功，数量: {}", skuList.size());
            }
        } else {
            // 单品
            TzProductSku defaultSku = TzProductMapping.INSTANCE.toSingleItemSku(productDetail, spuId);
            if (defaultSku != null) {
                if (defaultSku.getImage() == null && spu != null) {
                    // 优先使用白底图
                    if (spu.getWhiteImage() != null) {
                        defaultSku.setImage(spu.getWhiteImage());
                        log.debug("SKU图片为空，使用SPU白底图: {}", defaultSku.getImage());
                    } else if (spu.getMainImage() != null) {
                        defaultSku.setImage(spu.getMainImage());
                        log.debug("SKU图片为空，使用SPU主图片: {}", defaultSku.getImage());
                    }
                }

                this.tzProductSkuRepository.save(defaultSku);
                log.debug("重新创建单品默认SKU成功，skuId: {}", defaultSku.getId());
            }
        }
    }

    /**
     * 重新同步SPU的SKU数据
     */
    private boolean resyncSkusForSpu(Long spuId, String platformProductId) {
        try {
            // 获取产品详情
            Long productId = Long.valueOf(platformProductId);
            AlibabaProductDetailDTO productDetail = pdcProductMappingRepository
                .getProductDetailWithCache(productId, true); // 强制刷新缓存
            if (productDetail == null) {
                log.error("无法获取产品详情，重新同步SKU失败，spuId: {}, platformProductId: {}", spuId, platformProductId);
                return false;
            }

            // 创建SKU
            createSkusForSpu(spuId, productDetail);
            return true;
        } catch (Exception e) {
            log.error("重新同步SKU数据异常，spuId: {}, platformProductId: {}", spuId, platformProductId, e);
            return false;
        }
    }

    /**
     * 为SPU创建SKU并返回新的SKU列表
     */
    private List<TzProductSku> updateSkusForSpuAndReturn(Long spuId, AlibabaProductDetailDTO productDetail) {
        createSkusForSpu(spuId, productDetail);
        return getSkuListBySpuId(spuId);
    }

    /**
     * 智能更新SPU的SKU数据（增量同步，保护现有数据）
     *
     * <pre>
     * 优化策略：
     * 1. 通过 platformSku 字段匹配现有SKU和新SKU
     * 2. 更新现有SKU的数据（价格、库存、规格等）
     * 3. 新增缺少的SKU
     * 4. 保留现有SKU ID，避免订单数据关联问题
     * </pre>
     *
     * @param spuId         SPU ID
     * @param productDetail 最新产品详情
     * @return 更新后的SKU列表
     */
    private List<TzProductSku> intelligentUpdateSkusForSpu(Long spuId, AlibabaProductDetailDTO productDetail) {
        log.debug("开始智能更新SKU数据，spuId: {}", spuId);

        try {
            // 1. 获取现有SKU列表
            List<TzProductSku> existingSkuList = getSkuListBySpuId(spuId);
            log.debug("现有SKU数量: {}", existingSkuList.size());

            // 2. 构建新的SKU数据
            List<TzProductSku> newSkuDataList = buildNewSkuDataList(spuId, productDetail);
            log.debug("新SKU数据量: {}", newSkuDataList.size());

            // 3. 执行增量更新
            List<TzProductSku> updatedSkuList = performIncrementalSkuUpdate(existingSkuList, newSkuDataList);

            log.info("SKU智能更新完成，spuId: {}, 最终SKU数量: {}", spuId, updatedSkuList.size());
            return updatedSkuList;

        } catch (Exception e) {
            log.error("智能更新SKU失败，spuId: {}", spuId, e);
            // 降级到原有逻辑
            log.warn("降级使用原有SKU更新逻辑，spuId: {}", spuId);
            createSkusForSpu(spuId, productDetail);
            return getSkuListBySpuId(spuId);
        }
    }

    /**
     * 构建新的SKU数据列表
     */
    private List<TzProductSku> buildNewSkuDataList(Long spuId, AlibabaProductDetailDTO productDetail) {
        List<TzProductSku> newSkuDataList = new ArrayList<>();

        if (!productDetail.isSingleItem() && CollectionUtil.isNotEmpty(productDetail.getProductSkuList())) {
            // 多规格产品
            newSkuDataList = TzProductMapping.INSTANCE.toTzProductSkuList(
                productDetail.getProductSkuList(),
                spuId,
                productDetail.getPlatformProductId(),
                productDetail.getMinOrderQuantity()
            );
        } else {
            // 单品
            TzProductSku defaultSku = TzProductMapping.INSTANCE.toSingleItemSku(productDetail, spuId);
            if (defaultSku != null) {
                newSkuDataList.add(defaultSku);
            }
        }

        // 确保图片URL不为空
        TzProductSpu spu = this.tzProductSpuMapper.selectById(spuId);
        if (spu != null && CollectionUtil.isNotEmpty(newSkuDataList)) {
            newSkuDataList.forEach(sku -> {
                if (sku.getImage() == null) {
                    if (spu.getWhiteImage() != null) {
                        sku.setImage(spu.getWhiteImage());
                    } else if (spu.getMainImage() != null) {
                        sku.setImage(spu.getMainImage());
                    }
                }
            });
        }

        return newSkuDataList;
    }

    /**
     * 执行增量SKU更新
     */
    private List<TzProductSku> performIncrementalSkuUpdate(List<TzProductSku> existingSkuList, List<TzProductSku> newSkuDataList) {
        log.debug("执行增量SKU更新，现有SKU: {}, 新SKU数据: {}", existingSkuList.size(), newSkuDataList.size());

        // 使用Map来快速查找现有SKU
        Map<String, TzProductSku> existingSkuMap = existingSkuList.stream()
            .collect(Collectors.toMap(TzProductSku::getPlatformSku, sku -> sku, (existing, replacement) -> existing));

        List<TzProductSku> updatedSkuList = new ArrayList<>();
        List<TzProductSku> skusToCreate = new ArrayList<>();

        // 处理新SKU数据
        for (TzProductSku newSkuData : newSkuDataList) {
            String platformSku = newSkuData.getPlatformSku();
            TzProductSku existingSku = existingSkuMap.get(platformSku);

            if (existingSku != null) {
                // 更新现有SKU
                TzProductSku updatedSku = updateExistingSku(existingSku, newSkuData);
                updatedSkuList.add(updatedSku);
                log.debug("更新现有SKU: {}", platformSku);
            } else {
                // 需要创建新SKU
                skusToCreate.add(newSkuData);
                log.debug("标记创建新SKU: {}", platformSku);
            }
        }

        // 批量创建新SKU
        if (CollectionUtil.isNotEmpty(skusToCreate)) {
            try {
                this.tzProductSkuRepository.batchInsertSkus(skusToCreate);
                updatedSkuList.addAll(skusToCreate);
                log.info("批量创建新SKU成功，数量: {}", skusToCreate.size());
            } catch (Exception e) {
                log.error("批量创建SKU失败", e);
                // 尝试逐个创建
                for (TzProductSku sku : skusToCreate) {
                    try {
                        this.tzProductSkuRepository.save(sku);
                        updatedSkuList.add(sku);
                        log.debug("单个创建SKU成功: {}", sku.getPlatformSku());
                    } catch (Exception ex) {
                        log.error("创建SKU失败: {}", sku.getPlatformSku(), ex);
                    }
                }
            }
        }

        return updatedSkuList;
    }

    /**
     * 更新现有SKU数据
     */
    private TzProductSku updateExistingSku(TzProductSku existingSku, TzProductSku newSkuData) {
        log.debug("更新现有SKU数据: {}", existingSku.getPlatformSku());

        // 只更新需要同步的字段，保留现有的ID和其他关键信息
        existingSku.setSku(newSkuData.getSku());
        existingSku.setBarcode(newSkuData.getBarcode());
        existingSku.setPrice(newSkuData.getPrice());
        existingSku.setDropShippingPrice(newSkuData.getDropShippingPrice());
        existingSku.setQuantity(newSkuData.getQuantity());
        existingSku.setSpecs(newSkuData.getSpecs());

        // 更新图片，但保留现有图片如果新图片为空
        if (newSkuData.getImage() != null) {
            existingSku.setImage(newSkuData.getImage());
        }

        // 执行数据库更新
        try {
            boolean updateResult = this.tzProductSkuRepository.updateById(existingSku);
            if (updateResult) {
                log.debug("SKU更新成功: {}", existingSku.getPlatformSku());
            } else {
                log.warn("SKU更新失败，可能是乐观锁冲突: {}", existingSku.getPlatformSku());
            }
        } catch (Exception e) {
            log.error("SKU数据库更新异常: {}", existingSku.getPlatformSku(), e);
        }

        return existingSku;
    }

    // ==================== 实现新的统一数据获取方法 ====================

    @Override
    public TzProductDTO getOrSyncProductByPlatformId(String platformProductId) {
        log.debug("获取或同步产品数据: platformProductId={}", platformProductId);

        // 1.1 优先通过主键查询
        // 步骤1：检查SPU是否存在
        TzProductSpu existingSpu = Optional.ofNullable(this.tzProductSpuMapper.selectById(platformProductId)).orElseGet(() -> getExistingSpuByPlatformId(platformProductId));
        // 检查是否需要重新同步
        boolean isNeedResync = needResync(existingSpu);
        // 如果SPU存在且不需要重新同步，则直接返回
        if (!isNeedResync) {
            log.debug("从现有SPU获取产品数据: spuId={}", existingSpu.getId());
            return buildTzProductDTOFromSpu(existingSpu, platformProductId);
        }

        // 步骤2：检查PdcProductMapping映射关系
        Long productId = Optional.ofNullable(existingSpu).map(TzProductSpu::getPdcProductMappingId).orElse(Long.valueOf(platformProductId));
        AlibabaProductDetailDTO productDetail = pdcProductMappingRepository.getProductDetailWithCache(productId, isNeedResync);
        if (productDetail != null) {
            log.debug("从PdcProductMapping同步产品数据: platformProductId={}", platformProductId);
            return syncFromProductDetail(productDetail);
        }
        // 找不到数据
        log.warn("获取或同步产品数据失败: platformProductId={}", platformProductId);
        throw BusinessExceptionI18n.of(FulfillmenErrorCodeEnum.RESOURCE_NOT_FOUND);
    }

    /**
     * 检查TzProductSpu是否需要重新同步
     *
     * @param spu SPU实体
     * @return true-需要重新同步，false-数据仍然有效
     */
    private boolean needResync(TzProductSpu spu) {
        if (spu == null || spu.getGmtModified() == null) {
            return true;
        }

        // 检查数据是否超过3天未更新
        LocalDateTime lastModified = spu.getGmtModified();
        LocalDateTime now = LocalDateTime.now();
        Duration timeSinceUpdate = Duration.between(lastModified, now);

        boolean shouldResync = timeSinceUpdate.toDays() >= 3;

        if (shouldResync) {
            log.debug("TzProductSpu数据已过期，需要重新同步: spuId={}, 最后更新时间={}, 距今{}天", spu.getId(), lastModified, timeSinceUpdate
                .toDays());
        }

        return shouldResync;
    }

    @Override
    public AlibabaProductDetailDTO getAlibabaProductDetail(String platformProductId, boolean forceRefresh) {
        log.debug("获取阿里巴巴产品详情: platformProductId={}, forceRefresh={}", platformProductId, forceRefresh);

        try {
            Long productId = Long.valueOf(platformProductId);
            return pdcProductMappingRepository.getProductDetailWithCache(productId, forceRefresh);
        } catch (Exception e) {
            log.error("获取阿里巴巴产品详情失败: platformProductId={}", platformProductId, e);
            return null;
        }
    }

    /**
     * 检查是否需要重新同步数据
     *
     * <pre>
     * 🔥 优化后的智能检测逻辑：
     * 1. 优先通过 metaInfoHash 检测数据变更（MD5签名对比）
     * 2. 兜底使用时间间隔检测（超过3天）
     * 3. 避免不必要的API调用，提升性能
     * </pre>
     *
     * @param platformProductId 平台产品ID
     * @return 是否需要重新同步
     */
    private boolean checkIfNeedResync(String platformProductId) {
        try {
            // 检测是否 productId
            // 2. 🔥 智能检测：通过metaInfoHash检测数据变更
            if (hasDataChangedByHash(platformProductId)) {
                log.debug("检测到数据变更（基于metaInfoHash），需要重新同步: platformProductId={}", platformProductId);
                return true;
            }

            // 3. 兜底机制：检查时间间隔
            if (hasDataExpiredByTime(platformProductId)) {
                log.debug("数据已过期（基于时间检测），需要重新同步: platformProductId={}", platformProductId);
                return true;
            }

            log.debug("数据无需重新同步: platformProductId={}", platformProductId);
            return false;

        } catch (Exception e) {
            log.warn("检查数据同步状态失败，默认不同步: platformProductId={}", platformProductId, e);
            return false;
        }
    }

    /**
     * 🔥 通过metaInfoHash检测数据是否变更
     *
     * @param platformProductId 平台产品ID
     * @return true-数据已变更，false-数据未变更
     */
    private boolean hasDataChangedByHash(String platformProductId) {
        try {
            // 获取最新的商品详情数据
            // 强制刷新获取最新数据
            AlibabaProductDetailDTO latestProductDetail = pdcProductMappingRepository.getProductDetailWithCache(Long.valueOf(platformProductId), true);

            if (latestProductDetail == null) {
                log.debug("无法获取最新商品详情，跳过hash检测: {}", platformProductId);
                return false;
            }

            // 构建最新数据的metaInfo并计算MD5
            String latestHash = MetaInfoHashUtils.calculateMetaInfoHash(latestProductDetail);

            // 这里需要获取现有的metaInfoHash进行比较
            // 由于当前架构限制，暂时返回false，后续完善
            // TODO: 需要通过Repository获取现有的PdcProductMapping.metaInfoHash
            log.debug("计算得到最新hash: platformProductId={}, hash={}", platformProductId, latestHash);
            // 暂时返回false，后续完善
            return false;

        } catch (Exception e) {
            log.warn("metaInfoHash检测失败: platformProductId={}", platformProductId, e);
            return false;
        }
    }

    /**
     * 检查数据是否基于时间过期
     *
     * @param platformProductId 平台产品ID
     * @return true-已过期，false-未过期
     */
    private boolean hasDataExpiredByTime(String platformProductId) {
        try {
            // 通过Repository获取PdcProductMapping实体
            PdcProductMapping pdcProductMapping = pdcProductMappingRepository.getByPlatformProductId(platformProductId);

            if (pdcProductMapping == null) {
                log.debug("PdcProductMapping不存在，认为需要同步: platformProductId={}", platformProductId);
                return true; // 数据不存在，需要同步
            }

            LocalDateTime lastModified = pdcProductMapping.getGmtModified();
            if (lastModified == null) {
                log.debug("PdcProductMapping修改时间为空，认为需要同步: platformProductId={}", platformProductId);
                return true; // 修改时间为空，需要同步
            }

            // 检查数据是否超过3天未更新
            LocalDateTime now = LocalDateTime.now();
            Duration timeSinceUpdate = Duration.between(lastModified, now);
            boolean isExpired = timeSinceUpdate.toDays() >= 3;

            if (isExpired) {
                log.debug("PdcProductMapping数据已过期，需要重新同步: platformProductId={}, 最后更新时间={}, 距今{}天",
                    platformProductId, lastModified, timeSinceUpdate.toDays());
            } else {
                log.debug("PdcProductMapping数据未过期: platformProductId={}, 最后更新时间={}, 距今{}天",
                    platformProductId, lastModified, timeSinceUpdate.toDays());
            }

            return isExpired;

        } catch (Exception e) {
            log.warn("时间过期检测失败: platformProductId={}", platformProductId, e);
            return false; // 异常情况下不强制同步
        }
    }

    /**
     * 降级数据源：从阿里巴巴产品详情构建基础的TzProductDTO
     *
     * @param platformProductId 平台产品ID
     * @return 基础的TzProductDTO
     */
    private TzProductDTO getProductFromFallbackSource(String platformProductId) {
        try {
            log.warn("尝试从降级数据源获取产品: platformProductId={}", platformProductId);

            AlibabaProductDetailDTO alibabaDetail = getAlibabaProductDetail(platformProductId, false);
            if (alibabaDetail == null) {
                log.error("降级数据源也无法找到产品: platformProductId={}", platformProductId);
                return null;
            }

            // 构建基础的TzProductDTO
            String mainImage = null;
            if (alibabaDetail.getImages() != null && !alibabaDetail.getImages().isEmpty()) {
                mainImage = alibabaDetail.getImages().getFirst();
            } else if (alibabaDetail.getWhiteImage() != null) {
                mainImage = alibabaDetail.getWhiteImage();
            }

            TzProductDTO basicDTO = TzProductDTO.builder()
                .pdcPlatformProductId(platformProductId)
                .title(alibabaDetail.getTitle())
                .titleTrans(alibabaDetail.getTitleTrans())
                .mainImage(mainImage)
                .categoryId(alibabaDetail.getCategoryId())
                .categoryName(alibabaDetail.getCategoryName())
                .categoryNameTrans(alibabaDetail.getCategoryNameTrans())
                .description(alibabaDetail.getDescription())
                .unit(alibabaDetail.getUnit())
                .unitTrans(alibabaDetail.getUnitTrans())
                .whiteImage(alibabaDetail.getWhiteImage())
                .build();

            log.warn("降级数据源获取成功，返回基础产品信息: platformProductId={}", platformProductId);
            return basicDTO;

        } catch (Exception e) {
            log.error("降级数据源获取失败: platformProductId={}", platformProductId, e);
            return null;
        }
    }

    // ==================== 简化同步逻辑的辅助方法 ====================

    /**
     * 从现有SPU构建TzProductDTO
     */
    private TzProductDTO buildTzProductDTOFromSpu(TzProductSpu spu, String platformProductId) {
        try {
            Long productId = Long.valueOf(platformProductId);
            List<TzProductSku> existingSkuList = getSkuListBySpuId(spu.getId());
            AlibabaProductDetailDTO productDetail = pdcProductMappingRepository.getProductDetailWithCache(productId,
              false);
            TzProductDTO productDTO = TzProductMapping.INSTANCE.toTzProductDTO(spu, existingSkuList, productDetail);
            return applyProductStrategies(productDTO, null, null, platformProductId);
        } catch (Exception e) {
            log.error("从SPU构建DTO失败: spuId={}, platformProductId={}", spu.getId(), platformProductId, e);
            return null;
        }
    }

    /**
     * 从产品详情同步产品数据
     */
    private TzProductDTO syncFromProductDetail(AlibabaProductDetailDTO productDetail) {
        try {
            if (productDetail == null) {
                log.warn("产品详情为空，无法同步");
                return null;
            }

            // 执行同步逻辑，复用现有的syncProductByPlatformId方法
            return syncProductByPlatformId(productDetail.getPlatformProductId());

        } catch (Exception e) {
            log.error("从产品详情同步失败: productId={}", productDetail != null ? productDetail.getId() : "null", e);
            return null;
        }
    }

    /**
     * 应用产品策略处理
     *
     * @param productDTO        原始产品DTO
     * @param userId            用户ID（可选）
     * @param tenantId          租户ID（可选）
     * @param platformProductId 平台产品ID
     * @return 处理后的产品DTO
     */
    private TzProductDTO applyProductStrategies(TzProductDTO productDTO, Long userId, String tenantId, String platformProductId) {
        if (productDTO == null) {
            return null;
        }

        try {
            // 应用产品策略处理（使用默认用户和租户上下文）
            TzProductDTO processedProduct = productStrategyService.processProduct(productDTO, userId, tenantId, platformProductId);

            if (processedProduct != null) {
                log.debug("产品策略处理完成: 产品ID={}, 平台产品ID={}", productDTO.getId(), platformProductId);
                return processedProduct;
            } else {
                log.warn("产品策略处理返回空结果，使用原始产品: 平台产品ID={}", platformProductId);
                return productDTO;
            }

        } catch (Exception e) {
            log.error("产品策略处理异常，使用原始产品: 平台产品ID={}", platformProductId, e);
            return productDTO; // 策略处理失败时返回原始产品，确保系统可用性
        }
    }
}
