/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.product.service.impl;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.*;

import com.fulfillmen.shop.domain.dto.TzProductDTO;
import com.fulfillmen.shop.domain.dto.TzProductSkuDTO;
import com.fulfillmen.shop.manager.product.context.ProductProcessingContext;
import com.fulfillmen.shop.manager.product.context.TenantContext;
import com.fulfillmen.shop.manager.product.context.UserPricingContext;
import com.fulfillmen.shop.manager.product.service.IProductStrategyService;
import com.fulfillmen.shop.manager.service.IProductSyncService;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Stream;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

/**
 * ProductStrategyService 集成测试
 *
 * <pre>
 * 测试目标：
 * 1. 真实API调用测试 - 使用实际的商品ID和搜索关键词
 * 2. 端到端价格策略验证
 * 3. 数据库集成测试
 * 4. 缓存集成测试
 * 5. 完整业务流程测试
 * </pre>
 *
 * <AUTHOR>
 * @date 2025/08/22
 * @since 1.0.0
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
@DisplayName("产品策略服务集成测试")
class ProductStrategyServiceIntegrationTest {

    @Autowired
    private IProductStrategyService productStrategyService;

    @Autowired
    private IProductSyncService productSyncService;

    // 测试常量 - 真实的商品数据
    private static final String REAL_PRODUCT_ID = "724602447196220";
    private static final String SEARCH_KEYWORD = "合金模型小汽车";
    private static final Long TEST_USER_ID = 1001L;
    private static final String TEST_TENANT_ID = "integration_test_tenant";

    private UserPricingContext testUserContext;
    private TenantContext testTenantContext;

    @BeforeEach
    void setUp() {
        setupTestContexts();
    }

    private void setupTestContexts() {
        // 创建测试用户上下文
        testUserContext = UserPricingContext.builder()
            .userId(TEST_USER_ID)
            .userLevel(UserPricingContext.UserLevel.NORMAL)
            .markupRate(new BigDecimal("0.15"))
            .discountRate(BigDecimal.ONE)
            .isVip(false)
            .vipLevel(0)
            .totalPurchaseAmount(BigDecimal.ZERO)
            .totalOrderCount(0)
            .preferredCurrency("CNY")
            .build();

        // 创建测试租户上下文
        testTenantContext = TenantContext.builder()
            .tenantId(TEST_TENANT_ID)
            .tenantName("集成测试租户")
            .tenantType(TenantContext.TenantType.STANDARD)
            .tenantLevel(TenantContext.TenantLevel.BASIC)
            .defaultMarkupRate(new BigDecimal("0.15"))
            .minMarkupRate(new BigDecimal("0.05"))
            .maxMarkupRate(new BigDecimal("0.50"))
            .tenantDiscountRate(BigDecimal.ONE)
            .enablePersonalizedPricing(true)
            .enableDynamicPricing(false)
            .defaultCurrency("CNY")
            .build();
    }

    // ==================== 真实商品详情API测试 ====================

    @Nested
    @DisplayName("真实商品详情API测试")
    class RealProductDetailApiTest {

        @Test
        @EnabledIfSystemProperty(named = "integration.test.enabled", matches = "true")
        @DisplayName("测试真实商品ID的价格策略应用")
        void testRealProductDetailPricingStrategy() {
            // Given - 获取真实商品数据
            TzProductDTO realProduct = productSyncService.getOrSyncProductByPlatformId(REAL_PRODUCT_ID);
            
            // 如果无法获取真实数据，跳过测试
            if (realProduct == null) {
                System.out.println("跳过真实商品测试：无法获取商品数据 " + REAL_PRODUCT_ID);
                return;
            }

            // When
            TzProductDTO result = productStrategyService.applyPricingStrategy(realProduct, testUserContext);

            // Then
            assertThat(result).isNotNull();
            assertThat(result.getId()).isEqualTo(realProduct.getId());
            assertThat(result.getSkuList()).isNotEmpty();

            // 验证价格策略被应用
            result.getSkuList().forEach(sku -> {
                assertThat(sku.getDropShippingPrice()).isNotNull();
                if (sku.getPrice() != null && sku.getPrice().compareTo(BigDecimal.ZERO) > 0) {
                    // 验证价格确实被加价了
                    assertThat(sku.getDropShippingPrice()).isGreaterThan(sku.getPrice());
                }
            });

            System.out.printf("真实商品测试完成: 商品ID=%s, SKU数量=%d%n", 
                REAL_PRODUCT_ID, result.getSkuList().size());
        }

        @ParameterizedTest
        @EnabledIfSystemProperty(named = "integration.test.enabled", matches = "true")
        @DisplayName("测试不同用户类型对真实商品的价格策略")
        @MethodSource("provideRealUserScenarios")
        void testRealProductWithDifferentUserTypes(UserPricingContext.UserLevel userLevel,
                                                  boolean isVip,
                                                  BigDecimal expectedMinMarkup) {
            // Given
            TzProductDTO realProduct = productSyncService.getOrSyncProductByPlatformId(REAL_PRODUCT_ID);
            if (realProduct == null) {
                System.out.println("跳过用户类型测试：无法获取商品数据");
                return;
            }

            UserPricingContext userContext = UserPricingContext.builder()
                .userId(TEST_USER_ID)
                .userLevel(userLevel)
                .isVip(isVip)
                .markupRate(expectedMinMarkup)
                .build();

            // When
            TzProductDTO result = productStrategyService.applyPricingStrategy(realProduct, userContext);

            // Then
            assertThat(result).isNotNull();
            assertThat(result.getSkuList()).isNotEmpty();

            // 验证价格合理性
            result.getSkuList().forEach(sku -> {
                if (sku.getPrice() != null && sku.getPrice().compareTo(BigDecimal.ZERO) > 0) {
                    BigDecimal markupRatio = sku.getDropShippingPrice()
                        .divide(sku.getPrice(), 4, BigDecimal.ROUND_HALF_UP)
                        .subtract(BigDecimal.ONE);
                    
                    assertThat(markupRatio).isGreaterThanOrEqualTo(expectedMinMarkup.subtract(new BigDecimal("0.01")));
                }
            });

            System.out.printf("用户类型测试完成: 用户等级=%s, VIP=%s%n", userLevel, isVip);
        }

        static Stream<Arguments> provideRealUserScenarios() {
            return Stream.of(
                Arguments.of(UserPricingContext.UserLevel.NORMAL, false, new BigDecimal("0.10")),
                Arguments.of(UserPricingContext.UserLevel.GOLD, true, new BigDecimal("0.08")),
                Arguments.of(UserPricingContext.UserLevel.PLATINUM, true, new BigDecimal("0.05"))
            );
        }
    }

    // ==================== 真实搜索API测试 ====================

    @Nested
    @DisplayName("真实搜索API测试")
    class RealSearchApiTest {

        @Test
        @EnabledIfSystemProperty(named = "integration.test.enabled", matches = "true")
        @DisplayName("测试真实搜索关键词的批量价格处理")
        void testRealSearchKeywordBatchPricing() {
            // Given - 模拟搜索结果（实际应该从搜索API获取）
            List<String> searchResultIds = Arrays.asList(
                "724602447196220",
                "1600000000001", 
                "1600000000002"
            );

            // When
            List<TzProductDTO> processedResults = searchResultIds.stream()
                .map(productId -> {
                    TzProductDTO product = productSyncService.getOrSyncProductByPlatformId(productId);
                    if (product != null) {
                        return productStrategyService.applyPricingStrategy(product, testUserContext);
                    }
                    return null;
                })
                .filter(product -> product != null)
                .toList();

            // Then
            assertThat(processedResults).isNotEmpty();

            // 验证每个搜索结果都被正确处理
            processedResults.forEach(product -> {
                assertThat(product).isNotNull();
                assertThat(product.getSkuList()).isNotEmpty();
                
                product.getSkuList().forEach(sku -> {
                    assertThat(sku.getDropShippingPrice()).isNotNull();
                });
            });

            System.out.printf("搜索结果批量处理完成: 处理商品数量=%d%n", processedResults.size());
        }

        @Test
        @EnabledIfSystemProperty(named = "integration.test.enabled", matches = "true")
        @DisplayName("测试搜索结果的价格一致性验证")
        void testSearchResultPriceConsistencyValidation() {
            // Given
            String testProductId = REAL_PRODUCT_ID;
            TzProductDTO baseProduct = productSyncService.getOrSyncProductByPlatformId(testProductId);
            
            if (baseProduct == null) {
                System.out.println("跳过一致性测试：无法获取基础商品数据");
                return;
            }

            // When - 多次处理同一商品
            TzProductDTO result1 = productStrategyService.applyPricingStrategy(baseProduct, testUserContext);
            TzProductDTO result2 = productStrategyService.applyPricingStrategy(baseProduct, testUserContext);
            TzProductDTO result3 = productStrategyService.applyPricingStrategy(baseProduct, testUserContext);

            // Then - 验证价格一致性
            assertThat(result1).isNotNull();
            assertThat(result2).isNotNull();
            assertThat(result3).isNotNull();

            if (!result1.getSkuList().isEmpty() && !result2.getSkuList().isEmpty() && !result3.getSkuList().isEmpty()) {
                BigDecimal price1 = result1.getSkuList().get(0).getDropShippingPrice();
                BigDecimal price2 = result2.getSkuList().get(0).getDropShippingPrice();
                BigDecimal price3 = result3.getSkuList().get(0).getDropShippingPrice();

                assertThat(price1).isEqualTo(price2);
                assertThat(price2).isEqualTo(price3);

                System.out.printf("价格一致性验证通过: 价格=%s%n", price1);
            }
        }
    }

    // ==================== 端到端业务流程测试 ====================

    @Nested
    @DisplayName("端到端业务流程测试")
    class EndToEndBusinessFlowTest {

        @Test
        @EnabledIfSystemProperty(named = "integration.test.enabled", matches = "true")
        @DisplayName("测试完整的商品获取到定价流程")
        void testCompleteProductRetrievalToPricingFlow() {
            // Given
            String productId = REAL_PRODUCT_ID;

            // When - 完整流程：获取商品 -> 应用策略 -> 验证结果
            TzProductDTO originalProduct = productSyncService.getOrSyncProductByPlatformId(productId);
            
            if (originalProduct == null) {
                System.out.println("跳过端到端测试：无法获取原始商品数据");
                return;
            }

            TzProductDTO processedProduct = productStrategyService.applyPricingStrategy(originalProduct, testUserContext);

            // Then
            assertThat(processedProduct).isNotNull();
            assertThat(processedProduct.getId()).isEqualTo(originalProduct.getId());
            assertThat(processedProduct.getTitle()).isEqualTo(originalProduct.getTitle());

            // 验证业务逻辑正确性
            if (!processedProduct.getSkuList().isEmpty()) {
                TzProductSkuDTO firstSku = processedProduct.getSkuList().get(0);
                assertThat(firstSku.getDropShippingPrice()).isNotNull();
                
                if (firstSku.getPrice() != null && firstSku.getPrice().compareTo(BigDecimal.ZERO) > 0) {
                    // 验证加价逻辑
                    BigDecimal markupRatio = firstSku.getDropShippingPrice()
                        .divide(firstSku.getPrice(), 4, BigDecimal.ROUND_HALF_UP);
                    assertThat(markupRatio).isGreaterThan(BigDecimal.ONE);
                }
            }

            System.out.printf("端到端流程测试完成: 商品=%s, 标题=%s%n", 
                productId, processedProduct.getTitle());
        }

        @Test
        @DisplayName("测试系统健康状态检查")
        void testSystemHealthCheck() {
            // When
            String systemStatus = productStrategyService.getSystemStatus();
            String executionStats = productStrategyService.getExecutionStatistics();

            // Then
            assertThat(systemStatus).isNotNull();
            assertThat(executionStats).isNotNull();

            System.out.printf("系统状态: %s%n", systemStatus);
            System.out.printf("执行统计: %s%n", executionStats);
        }
    }
}
