/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.product.service.impl;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.fulfillmen.shop.common.context.UserContextHolder;
import com.fulfillmen.shop.common.tenant.EnhancedTenantContextHolder;
import com.fulfillmen.shop.domain.dto.TzProductDTO;
import com.fulfillmen.shop.domain.dto.TzProductSkuDTO;
import com.fulfillmen.shop.manager.product.chain.ProductProcessor;
import com.fulfillmen.shop.manager.product.chain.ProductProcessorChain;
import com.fulfillmen.shop.manager.product.context.ProductProcessingContext;
import com.fulfillmen.shop.manager.product.context.TenantContext;
import com.fulfillmen.shop.manager.product.context.UserPricingContext;
import com.fulfillmen.shop.manager.product.strategy.ProductStrategyFactory;
import com.fulfillmen.shop.manager.product.strategy.pricing.PricingStrategy;
import com.fulfillmen.shop.manager.product.strategy.pricing.impl.DefaultPricingStrategy;
import com.fulfillmen.shop.manager.product.strategy.pricing.impl.TenantPricingStrategy;
import com.fulfillmen.shop.manager.product.strategy.pricing.impl.UserPricingStrategy;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Stream;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.CsvSource;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.test.context.ActiveProfiles;

/**
 * ProductStrategyServiceImpl 完整测试套件
 *
 * <pre>
 * 测试范围：
 * 1. 商品详情API测试 - 验证不同用户类型的价格策略应用
 * 2. 商品搜索API测试 - 验证批量商品的价格处理
 * 3. 策略工厂智能选择逻辑测试
 * 4. 处理器链执行测试
 * 5. 缓存机制测试
 * 6. 并发场景测试
 * 7. 异常处理测试
 * </pre>
 *
 * <AUTHOR>
 * @date 2025/08/22
 * @since 1.0.0
 */
@ExtendWith(MockitoExtension.class)
@SpringBootTest
@ActiveProfiles("test")
@DisplayName("产品策略服务完整测试套件")
class ProductStrategyServiceImplTest {

    @Mock
    private ProductProcessorChain processorChain;

    @Mock
    private ProductStrategyFactory strategyFactory;

    @Mock
    private CacheManager cacheManager;

    @Mock
    private Cache cache;

    @InjectMocks
    private ProductStrategyServiceImpl productStrategyService;

    // 测试策略实例
    @Mock
    private DefaultPricingStrategy defaultPricingStrategy;

    @Mock
    private UserPricingStrategy userPricingStrategy;

    @Mock
    private TenantPricingStrategy tenantPricingStrategy;

    // 测试数据
    private TzProductDTO testProduct;
    private UserPricingContext testUserContext;
    private TenantContext testTenantContext;
    private ProductProcessingContext testProcessingContext;

    // 测试常量
    private static final String TEST_PRODUCT_ID = "724602447196220";
    private static final String SEARCH_KEYWORD = "合金模型小汽车";
    private static final Long TEST_USER_ID = 1001L;
    private static final String TEST_TENANT_ID = "tenant_001";

    @BeforeEach
    void setUp() {
        setupTestData();
        setupMockBehaviors();
    }

    /**
     * 设置测试数据
     */
    private void setupTestData() {
        // 创建测试产品
        testProduct = createTestProduct(TEST_PRODUCT_ID, "测试商品", new BigDecimal("100.00"));

        // 创建用户定价上下文
        testUserContext = UserPricingContext.builder()
            .userId(TEST_USER_ID)
            .userLevel(UserPricingContext.UserLevel.NORMAL)
            .markupRate(new BigDecimal("0.15"))
            .discountRate(BigDecimal.ONE)
            .isVip(false)
            .vipLevel(0)
            .totalPurchaseAmount(BigDecimal.ZERO)
            .totalOrderCount(0)
            .preferredCurrency("CNY")
            .build();

        // 创建租户上下文
        testTenantContext = TenantContext.builder()
            .tenantId(TEST_TENANT_ID)
            .tenantName("测试租户")
            .tenantType(TenantContext.TenantType.STANDARD)
            .tenantLevel(TenantContext.TenantLevel.BASIC)
            .defaultMarkupRate(new BigDecimal("0.15"))
            .minMarkupRate(new BigDecimal("0.05"))
            .maxMarkupRate(new BigDecimal("0.50"))
            .tenantDiscountRate(BigDecimal.ONE)
            .enablePersonalizedPricing(true)
            .enableDynamicPricing(false)
            .defaultCurrency("CNY")
            .build();

        // 创建处理上下文
        testProcessingContext = ProductProcessingContext.builder()
            .userId(TEST_USER_ID)
            .tenantId(TEST_TENANT_ID)
            .platformProductId(TEST_PRODUCT_ID)
            .productId(testProduct.getId())
            .userPricingContext(testUserContext)
            .tenantContext(testTenantContext)
            .processingStartTime(LocalDateTime.now())
            .build();
    }

    /**
     * 设置Mock行为
     */
    private void setupMockBehaviors() {
        // 设置缓存Mock
        when(cacheManager.getCache(anyString())).thenReturn(cache);
        when(cache.get(anyString())).thenReturn(null);

        // 设置处理器链Mock
        when(processorChain.processProduct(any(ProductProcessingContext.class)))
            .thenReturn(ProductProcessor.ProcessResult.success("处理成功"));

        // 设置策略工厂Mock
        when(strategyFactory.getBestPricingStrategy(any(ProductProcessingContext.class)))
            .thenReturn(defaultPricingStrategy);

        // 设置默认定价策略Mock
        when(defaultPricingStrategy.process(any(TzProductDTO.class), any(ProductProcessingContext.class)))
            .thenAnswer(invocation -> {
                TzProductDTO product = invocation.getArgument(0);
                return applyDefaultPricing(product);
            });

        when(defaultPricingStrategy.calculatePrice(any(BigDecimal.class), any(UserPricingContext.class)))
            .thenAnswer(invocation -> {
                BigDecimal originalPrice = invocation.getArgument(0);
                return originalPrice.multiply(new BigDecimal("1.15")); // 15%加价
            });

        when(defaultPricingStrategy.getStrategyName()).thenReturn("默认定价策略");
        when(defaultPricingStrategy.getPriority()).thenReturn(100);
    }

    /**
     * 创建测试产品
     */
    private TzProductDTO createTestProduct(String platformProductId, String title, BigDecimal basePrice) {
        TzProductDTO product = new TzProductDTO();
        product.setId(1L);
        product.setTitle(title);
        product.setPdcPlatformProductId(platformProductId);

        // 创建测试SKU
        TzProductSkuDTO sku1 = new TzProductSkuDTO();
        sku1.setId(1L);
        sku1.setSku("SKU_001");
        sku1.setPrice(basePrice);
        sku1.setDropShippingPrice(basePrice);

        TzProductSkuDTO sku2 = new TzProductSkuDTO();
        sku2.setId(2L);
        sku2.setSku("SKU_002");
        sku2.setPrice(basePrice.multiply(new BigDecimal("2")));
        sku2.setDropShippingPrice(basePrice.multiply(new BigDecimal("2")));

        product.setSkuList(Arrays.asList(sku1, sku2));
        return product;
    }

    /**
     * 应用默认定价逻辑
     */
    private TzProductDTO applyDefaultPricing(TzProductDTO product) {
        if (product.getSkuList() != null) {
            product.getSkuList().forEach(sku -> {
                if (sku.getPrice() != null) {
                    BigDecimal newPrice = sku.getPrice().multiply(new BigDecimal("1.15"));
                    sku.setDropShippingPrice(newPrice);
                }
            });
        }
        return product;
    }

    // ==================== 商品详情API测试 ====================

    @Nested
    @DisplayName("商品详情API测试")
    class ProductDetailApiTest {

        @Test
        @DisplayName("测试指定商品ID的价格策略应用")
        void testProductDetailPricingStrategy() {
            // Given
            TzProductDTO inputProduct = createTestProduct(TEST_PRODUCT_ID, "测试商品详情", new BigDecimal("100.00"));

            // When
            TzProductDTO result = productStrategyService.processProduct(inputProduct, testProcessingContext);

            // Then
            assertThat(result).isNotNull();
            assertThat(result.getSkuList()).hasSize(2);

            // 验证价格策略应用
            TzProductSkuDTO firstSku = result.getSkuList().get(0);
            assertThat(firstSku.getDropShippingPrice()).isEqualTo(new BigDecimal("115.00"));

            TzProductSkuDTO secondSku = result.getSkuList().get(1);
            assertThat(secondSku.getDropShippingPrice()).isEqualTo(new BigDecimal("230.00"));

            // 验证策略调用
            verify(strategyFactory).getBestPricingStrategy(any(ProductProcessingContext.class));
            verify(defaultPricingStrategy).process(eq(inputProduct), any(ProductProcessingContext.class));
        }

        @ParameterizedTest
        @DisplayName("测试不同用户类型的价格策略")
        @MethodSource("provideUserTypeTestData")
        void testDifferentUserTypePricingStrategies(UserPricingContext.UserLevel userLevel,
                                                   boolean isVip,
                                                   PricingStrategy expectedStrategy,
                                                   BigDecimal expectedMultiplier) {
            // Given
            UserPricingContext userContext = UserPricingContext.builder()
                .userId(TEST_USER_ID)
                .userLevel(userLevel)
                .isVip(isVip)
                .markupRate(new BigDecimal("0.15"))
                .build();

            ProductProcessingContext context = ProductProcessingContext.builder()
                .userId(TEST_USER_ID)
                .userPricingContext(userContext)
                .productId(testProduct.getId())
                .build();

            when(strategyFactory.getBestPricingStrategy(any(ProductProcessingContext.class)))
                .thenReturn(expectedStrategy);

            when(expectedStrategy.process(any(TzProductDTO.class), any(ProductProcessingContext.class)))
                .thenAnswer(invocation -> {
                    TzProductDTO product = invocation.getArgument(0);
                    product.getSkuList().forEach(sku -> {
                        if (sku.getPrice() != null) {
                            sku.setDropShippingPrice(sku.getPrice().multiply(expectedMultiplier));
                        }
                    });
                    return product;
                });

            // When
            TzProductDTO result = productStrategyService.applyPricingStrategy(testProduct, userContext);

            // Then
            assertThat(result).isNotNull();
            assertThat(result.getSkuList()).isNotEmpty();

            BigDecimal expectedPrice = new BigDecimal("100.00").multiply(expectedMultiplier);
            assertThat(result.getSkuList().get(0).getDropShippingPrice()).isEqualTo(expectedPrice);

            verify(strategyFactory).getBestPricingStrategy(any(ProductProcessingContext.class));
            verify(expectedStrategy).process(eq(testProduct), any(ProductProcessingContext.class));
        }

        static Stream<Arguments> provideUserTypeTestData() {
            return Stream.of(
                Arguments.of(
                    UserPricingContext.UserLevel.NORMAL,
                    false,
                    mock(DefaultPricingStrategy.class),
                    new BigDecimal("1.15") // 15%加价
                ),
                Arguments.of(
                    UserPricingContext.UserLevel.GOLD,
                    true,
                    mock(UserPricingStrategy.class),
                    new BigDecimal("1.12") // 12%加价
                ),
                Arguments.of(
                    UserPricingContext.UserLevel.PLATINUM,
                    true,
                    mock(UserPricingStrategy.class),
                    new BigDecimal("1.10") // 10%加价
                )
            );
        }

        @Test
        @DisplayName("测试异常情况处理 - 无效商品ID")
        void testInvalidProductIdHandling() {
            // Given
            TzProductDTO nullProduct = null;

            // When
            TzProductDTO result = productStrategyService.applyPricingStrategy(nullProduct, testUserContext);

            // Then
            assertThat(result).isNull();

            // 验证不会调用策略
            verify(strategyFactory, never()).getBestPricingStrategy(any(ProductProcessingContext.class));
        }

        @Test
        @DisplayName("测试价格为空的异常处理")
        void testNullPriceHandling() {
            // Given
            TzProductDTO productWithNullPrice = createTestProduct(TEST_PRODUCT_ID, "空价格商品", null);
            productWithNullPrice.getSkuList().get(0).setPrice(null);
            productWithNullPrice.getSkuList().get(0).setDropShippingPrice(null);

            when(defaultPricingStrategy.process(any(TzProductDTO.class), any(ProductProcessingContext.class)))
                .thenReturn(productWithNullPrice);

            // When
            TzProductDTO result = productStrategyService.processProduct(productWithNullPrice, testProcessingContext);

            // Then
            assertThat(result).isNotNull();
            assertThat(result.getSkuList().get(0).getPrice()).isNull();

            verify(strategyFactory).getBestPricingStrategy(any(ProductProcessingContext.class));
        }

        @Test
        @DisplayName("测试价格计算准确性验证")
        void testPriceCalculationAccuracy() {
            // Given
            TzProductDTO precisionTestProduct = createTestProduct("PRECISION_TEST", "精度测试商品", new BigDecimal("99.99"));

            // When
            TzProductDTO result = productStrategyService.processProduct(precisionTestProduct, testProcessingContext);

            // Then
            assertThat(result).isNotNull();

            // 验证价格精度（保留2位小数）
            BigDecimal expectedPrice = new BigDecimal("99.99").multiply(new BigDecimal("1.15"));
            assertThat(result.getSkuList().get(0).getDropShippingPrice()).isEqualTo(new BigDecimal("114.99"));

            // 验证第二个SKU的价格
            BigDecimal expectedPrice2 = new BigDecimal("199.98").multiply(new BigDecimal("1.15"));
            assertThat(result.getSkuList().get(1).getDropShippingPrice()).isEqualTo(new BigDecimal("229.97"));
        }
    }

    // ==================== 商品搜索API测试 ====================

    @Nested
    @DisplayName("商品搜索API测试")
    class ProductSearchApiTest {

        @Test
        @DisplayName("测试搜索结果批量价格处理")
        void testBatchProductPricingInSearchResults() {
            // Given
            List<TzProductDTO> searchResults = Arrays.asList(
                createTestProduct("001", "合金模型小汽车A", new BigDecimal("50.00")),
                createTestProduct("002", "合金模型小汽车B", new BigDecimal("80.00")),
                createTestProduct("003", "合金模型小汽车C", new BigDecimal("120.00"))
            );

            // When
            List<TzProductDTO> processedResults = searchResults.stream()
                .map(product -> productStrategyService.processProduct(product, testProcessingContext))
                .toList();

            // Then
            assertThat(processedResults).hasSize(3);

            // 验证每个商品的价格策略都被应用
            assertThat(processedResults.get(0).getSkuList().get(0).getDropShippingPrice())
                .isEqualTo(new BigDecimal("57.50")); // 50 * 1.15

            assertThat(processedResults.get(1).getSkuList().get(0).getDropShippingPrice())
                .isEqualTo(new BigDecimal("92.00")); // 80 * 1.15

            assertThat(processedResults.get(2).getSkuList().get(0).getDropShippingPrice())
                .isEqualTo(new BigDecimal("138.00")); // 120 * 1.15

            // 验证策略调用次数
            verify(strategyFactory, times(3)).getBestPricingStrategy(any(ProductProcessingContext.class));
        }

        @Test
        @DisplayName("测试批量商品价格处理性能")
        void testBatchProductPricingPerformance() {
            // Given
            List<TzProductDTO> largeProductList = Stream.generate(() ->
                    createTestProduct("PERF_" + System.nanoTime(), "性能测试商品", new BigDecimal("100.00")))
                .limit(100)
                .toList();

            // When
            long startTime = System.currentTimeMillis();
            List<TzProductDTO> processedResults = largeProductList.stream()
                .map(product -> productStrategyService.processProduct(product, testProcessingContext))
                .toList();
            long endTime = System.currentTimeMillis();

            // Then
            assertThat(processedResults).hasSize(100);
            assertThat(endTime - startTime).isLessThan(5000); // 应在5秒内完成

            // 验证所有商品都被处理
            processedResults.forEach(product -> {
                assertThat(product.getSkuList()).isNotEmpty();
                assertThat(product.getSkuList().get(0).getDropShippingPrice()).isNotNull();
            });
        }

        @Test
        @DisplayName("测试搜索结果价格一致性")
        void testSearchResultPriceConsistency() {
            // Given
            TzProductDTO baseProduct = createTestProduct("CONSISTENCY_TEST", "一致性测试商品", new BigDecimal("100.00"));

            // When - 多次处理同一商品
            TzProductDTO result1 = productStrategyService.processProduct(baseProduct, testProcessingContext);
            TzProductDTO result2 = productStrategyService.processProduct(baseProduct, testProcessingContext);
            TzProductDTO result3 = productStrategyService.processProduct(baseProduct, testProcessingContext);

            // Then - 价格应该一致
            BigDecimal price1 = result1.getSkuList().get(0).getDropShippingPrice();
            BigDecimal price2 = result2.getSkuList().get(0).getDropShippingPrice();
            BigDecimal price3 = result3.getSkuList().get(0).getDropShippingPrice();

            assertThat(price1).isEqualTo(price2);
            assertThat(price2).isEqualTo(price3);
            assertThat(price1).isEqualTo(new BigDecimal("115.00"));
        }

        @ParameterizedTest
        @DisplayName("测试不同价格区间的批量处理")
        @CsvSource({
            "10.00, 11.50",
            "50.00, 57.50",
            "100.00, 115.00",
            "500.00, 575.00",
            "1000.00, 1150.00"
        })
        void testBatchProcessingWithDifferentPriceRanges(BigDecimal originalPrice, BigDecimal expectedPrice) {
            // Given
            TzProductDTO product = createTestProduct("RANGE_TEST", "价格区间测试", originalPrice);

            // When
            TzProductDTO result = productStrategyService.processProduct(product, testProcessingContext);

            // Then
            assertThat(result.getSkuList().get(0).getDropShippingPrice()).isEqualTo(expectedPrice);
        }
    }

    // ==================== 策略工厂智能选择测试 ====================

    @Nested
    @DisplayName("策略工厂智能选择测试")
    class StrategyFactoryIntelligentSelectionTest {

        @Test
        @DisplayName("测试已登录用户策略选择")
        void testLoggedInUserStrategySelection() {
            // Given
            try (MockedStatic<UserContextHolder> mockedUserContext = mockStatic(UserContextHolder.class)) {
                mockedUserContext.when(UserContextHolder::getUserId).thenReturn(TEST_USER_ID);
                mockedUserContext.when(UserContextHolder::getServiceFeeRate).thenReturn(new BigDecimal("0.12"));

                when(strategyFactory.getBestPricingStrategy(any(ProductProcessingContext.class)))
                    .thenReturn(userPricingStrategy);

                when(userPricingStrategy.process(any(TzProductDTO.class), any(ProductProcessingContext.class)))
                    .thenAnswer(invocation -> {
                        TzProductDTO product = invocation.getArgument(0);
                        return applyUserPricing(product, new BigDecimal("0.12"));
                    });

                // When
                TzProductDTO result = productStrategyService.applyPricingStrategy(testProduct, testUserContext);

                // Then
                assertThat(result).isNotNull();
                assertThat(result.getSkuList().get(0).getDropShippingPrice())
                    .isEqualTo(new BigDecimal("112.00")); // 100 * 1.12

                verify(strategyFactory).getBestPricingStrategy(any(ProductProcessingContext.class));
                verify(userPricingStrategy).process(eq(testProduct), any(ProductProcessingContext.class));
            }
        }

        @Test
        @DisplayName("测试未登录用户策略选择")
        void testAnonymousUserStrategySelection() {
            // Given
            try (MockedStatic<UserContextHolder> mockedUserContext = mockStatic(UserContextHolder.class);
                 MockedStatic<EnhancedTenantContextHolder> mockedTenantContext = mockStatic(EnhancedTenantContextHolder.class)) {

                mockedUserContext.when(UserContextHolder::getUserId).thenReturn(null);
                mockedTenantContext.when(EnhancedTenantContextHolder::getCurrentTenantId).thenReturn(TEST_TENANT_ID);

                when(strategyFactory.getBestPricingStrategy(any(ProductProcessingContext.class)))
                    .thenReturn(tenantPricingStrategy);

                when(tenantPricingStrategy.process(any(TzProductDTO.class), any(ProductProcessingContext.class)))
                    .thenAnswer(invocation -> {
                        TzProductDTO product = invocation.getArgument(0);
                        return applyTenantPricing(product, new BigDecimal("0.15"));
                    });

                // When
                ProductProcessingContext anonymousContext = ProductProcessingContext.builder()
                    .tenantId(TEST_TENANT_ID)
                    .tenantContext(testTenantContext)
                    .productId(testProduct.getId())
                    .build();

                TzProductDTO result = productStrategyService.processProduct(testProduct, anonymousContext);

                // Then
                assertThat(result).isNotNull();
                assertThat(result.getSkuList().get(0).getDropShippingPrice())
                    .isEqualTo(new BigDecimal("115.00")); // 100 * 1.15

                verify(strategyFactory).getBestPricingStrategy(any(ProductProcessingContext.class));
                verify(tenantPricingStrategy).process(eq(testProduct), any(ProductProcessingContext.class));
            }
        }

        private TzProductDTO applyUserPricing(TzProductDTO product, BigDecimal serviceRate) {
            if (product.getSkuList() != null) {
                product.getSkuList().forEach(sku -> {
                    if (sku.getPrice() != null) {
                        BigDecimal newPrice = sku.getPrice().multiply(BigDecimal.ONE.add(serviceRate));
                        sku.setDropShippingPrice(newPrice);
                    }
                });
            }
            return product;
        }

        private TzProductDTO applyTenantPricing(TzProductDTO product, BigDecimal markupRate) {
            if (product.getSkuList() != null) {
                product.getSkuList().forEach(sku -> {
                    if (sku.getPrice() != null) {
                        BigDecimal newPrice = sku.getPrice().multiply(BigDecimal.ONE.add(markupRate));
                        sku.setDropShippingPrice(newPrice);
                    }
                });
            }
            return product;
        }

        @Test
        @DisplayName("测试策略回退机制")
        void testStrategyFallbackMechanism() {
            // Given - 策略工厂返回null，应该回退到默认处理
            when(strategyFactory.getBestPricingStrategy(any(ProductProcessingContext.class)))
                .thenReturn(null);

            // When
            TzProductDTO result = productStrategyService.applyPricingStrategy(testProduct, testUserContext);

            // Then - 应该返回原始产品（无策略应用）
            assertThat(result).isEqualTo(testProduct);

            verify(strategyFactory).getBestPricingStrategy(any(ProductProcessingContext.class));
        }
    }

    // ==================== 处理器链执行测试 ====================

    @Nested
    @DisplayName("处理器链执行测试")
    class ProcessorChainExecutionTest {

        @Test
        @DisplayName("测试处理器链正常执行")
        void testProcessorChainNormalExecution() {
            // Given
            when(processorChain.processProduct(any(ProductProcessingContext.class)))
                .thenReturn(ProductProcessor.ProcessResult.success("处理链执行成功"));

            // When
            TzProductDTO result = productStrategyService.processProduct(testProduct, testProcessingContext);

            // Then
            assertThat(result).isNotNull();
            verify(processorChain).processProduct(any(ProductProcessingContext.class));
            verify(strategyFactory).getBestPricingStrategy(any(ProductProcessingContext.class));
        }

        @Test
        @DisplayName("测试处理器链执行失败")
        void testProcessorChainExecutionFailure() {
            // Given
            when(processorChain.processProduct(any(ProductProcessingContext.class)))
                .thenReturn(ProductProcessor.ProcessResult.failure("处理链执行失败"));

            // When
            TzProductDTO result = productStrategyService.processProduct(testProduct, testProcessingContext);

            // Then
            assertThat(result).isNotNull(); // 即使处理器链失败，仍应返回处理后的产品
            verify(processorChain).processProduct(any(ProductProcessingContext.class));
        }

        @Test
        @DisplayName("测试处理器链执行顺序")
        void testProcessorChainExecutionOrder() {
            // Given
            ProductProcessingContext capturedContext = null;

            when(processorChain.processProduct(any(ProductProcessingContext.class)))
                .thenAnswer(invocation -> {
                    ProductProcessingContext context = invocation.getArgument(0);
                    // 验证上下文包含产品信息
                    assertThat(context.getAttribute("product")).isNotNull();
                    return ProductProcessor.ProcessResult.success("处理完成");
                });

            // When
            TzProductDTO result = productStrategyService.processProduct(testProduct, testProcessingContext);

            // Then
            assertThat(result).isNotNull();
            verify(processorChain).processProduct(any(ProductProcessingContext.class));
        }
    }

    // ==================== 缓存机制测试 ====================

    @Nested
    @DisplayName("缓存机制测试")
    class CacheMechanismTest {

        @Test
        @DisplayName("测试缓存命中")
        void testCacheHit() {
            // Given
            String cacheKey = "strategy_cache_" + TEST_PRODUCT_ID;
            when(cache.get(cacheKey)).thenReturn("cached_strategy_result");

            // When
            TzProductDTO result = productStrategyService.processProduct(testProduct, testProcessingContext);

            // Then
            assertThat(result).isNotNull();
            verify(cacheManager).getCache(anyString());
        }

        @Test
        @DisplayName("测试缓存未命中")
        void testCacheMiss() {
            // Given
            String cacheKey = "strategy_cache_" + TEST_PRODUCT_ID;
            when(cache.get(cacheKey)).thenReturn(null);

            // When
            TzProductDTO result = productStrategyService.processProduct(testProduct, testProcessingContext);

            // Then
            assertThat(result).isNotNull();
            verify(cacheManager).getCache(anyString());
            verify(strategyFactory).getBestPricingStrategy(any(ProductProcessingContext.class));
        }

        @Test
        @DisplayName("测试缓存更新")
        void testCacheUpdate() {
            // Given
            String cacheKey = "strategy_cache_" + TEST_PRODUCT_ID;
            when(cache.get(cacheKey)).thenReturn(null);

            // When
            TzProductDTO result1 = productStrategyService.processProduct(testProduct, testProcessingContext);
            TzProductDTO result2 = productStrategyService.processProduct(testProduct, testProcessingContext);

            // Then
            assertThat(result1).isNotNull();
            assertThat(result2).isNotNull();
            verify(cacheManager, atLeast(2)).getCache(anyString());
        }
    }

    // ==================== 并发场景测试 ====================

    @Nested
    @DisplayName("并发场景测试")
    class ConcurrentScenarioTest {

        @Test
        @DisplayName("测试并发价格计算准确性")
        void testConcurrentPriceCalculationAccuracy() throws InterruptedException {
            // Given
            int threadCount = 10;
            CountDownLatch latch = new CountDownLatch(threadCount);
            ExecutorService executor = Executors.newFixedThreadPool(threadCount);
            List<CompletableFuture<TzProductDTO>> futures = new ArrayList<>();

            // When
            for (int i = 0; i < threadCount; i++) {
                CompletableFuture<TzProductDTO> future = CompletableFuture.supplyAsync(() -> {
                    try {
                        return productStrategyService.processProduct(testProduct, testProcessingContext);
                    } finally {
                        latch.countDown();
                    }
                }, executor);
                futures.add(future);
            }

            latch.await();

            // Then
            List<TzProductDTO> results = futures.stream()
                .map(CompletableFuture::join)
                .toList();

            assertThat(results).hasSize(threadCount);

            // 验证所有结果的价格一致性
            BigDecimal expectedPrice = new BigDecimal("115.00");
            results.forEach(result -> {
                assertThat(result).isNotNull();
                assertThat(result.getSkuList().get(0).getDropShippingPrice()).isEqualTo(expectedPrice);
            });

            executor.shutdown();
        }

        @Test
        @DisplayName("测试并发策略选择")
        void testConcurrentStrategySelection() throws InterruptedException {
            // Given
            int threadCount = 20;
            CountDownLatch latch = new CountDownLatch(threadCount);
            ExecutorService executor = Executors.newFixedThreadPool(threadCount);
            List<CompletableFuture<TzProductDTO>> futures = new ArrayList<>();

            // When
            for (int i = 0; i < threadCount; i++) {
                final int threadIndex = i;
                CompletableFuture<TzProductDTO> future = CompletableFuture.supplyAsync(() -> {
                    try {
                        // 创建不同的用户上下文
                        UserPricingContext userContext = UserPricingContext.builder()
                            .userId(TEST_USER_ID + threadIndex)
                            .userLevel(UserPricingContext.UserLevel.NORMAL)
                            .markupRate(new BigDecimal("0.15"))
                            .build();

                        return productStrategyService.applyPricingStrategy(testProduct, userContext);
                    } finally {
                        latch.countDown();
                    }
                }, executor);
                futures.add(future);
            }

            latch.await();

            // Then
            List<TzProductDTO> results = futures.stream()
                .map(CompletableFuture::join)
                .toList();

            assertThat(results).hasSize(threadCount);
            results.forEach(result -> assertThat(result).isNotNull());

            // 验证策略工厂被调用了正确的次数
            verify(strategyFactory, times(threadCount)).getBestPricingStrategy(any(ProductProcessingContext.class));

            executor.shutdown();
        }
    }

    // ==================== 异常处理测试 ====================

    @Nested
    @DisplayName("异常处理测试")
    class ExceptionHandlingTest {

        @Test
        @DisplayName("测试策略执行异常处理")
        void testStrategyExecutionExceptionHandling() {
            // Given
            when(defaultPricingStrategy.process(any(TzProductDTO.class), any(ProductProcessingContext.class)))
                .thenThrow(new RuntimeException("策略执行异常"));

            // When
            TzProductDTO result = productStrategyService.applyPricingStrategy(testProduct, testUserContext);

            // Then - 应该返回原始产品，不抛出异常
            assertThat(result).isEqualTo(testProduct);
            verify(strategyFactory).getBestPricingStrategy(any(ProductProcessingContext.class));
        }

        @Test
        @DisplayName("测试处理器链异常处理")
        void testProcessorChainExceptionHandling() {
            // Given
            when(processorChain.processProduct(any(ProductProcessingContext.class)))
                .thenThrow(new RuntimeException("处理器链异常"));

            // When & Then - 应该不抛出异常
            assertDoesNotThrow(() -> {
                TzProductDTO result = productStrategyService.processProduct(testProduct, testProcessingContext);
                assertThat(result).isNotNull();
            });
        }

        @Test
        @DisplayName("测试空指针异常处理")
        void testNullPointerExceptionHandling() {
            // Given
            TzProductDTO nullProduct = null;
            UserPricingContext nullContext = null;

            // When & Then
            assertDoesNotThrow(() -> {
                TzProductDTO result1 = productStrategyService.applyPricingStrategy(nullProduct, testUserContext);
                assertThat(result1).isNull();

                TzProductDTO result2 = productStrategyService.applyPricingStrategy(testProduct, nullContext);
                assertThat(result2).isNull();
            });
        }

        @Test
        @DisplayName("测试数据完整性验证")
        void testDataIntegrityValidation() {
            // Given - 创建不完整的产品数据
            TzProductDTO incompleteProduct = new TzProductDTO();
            incompleteProduct.setId(1L);
            incompleteProduct.setTitle("不完整产品");
            // 故意不设置SKU列表

            // When
            TzProductDTO result = productStrategyService.processProduct(incompleteProduct, testProcessingContext);

            // Then - 应该能处理不完整的数据
            assertThat(result).isNotNull();
            assertThat(result.getId()).isEqualTo(1L);
        }
    }

    // ==================== 集成测试 ====================

    @Nested
    @DisplayName("集成测试")
    class IntegrationTest {

        @Test
        @DisplayName("测试完整的产品策略处理流程")
        void testCompleteProductStrategyProcessingFlow() {
            // Given
            TzProductDTO complexProduct = createComplexTestProduct();

            // When
            TzProductDTO result = productStrategyService.processProduct(complexProduct, testProcessingContext);

            // Then
            assertThat(result).isNotNull();
            assertThat(result.getSkuList()).hasSize(3);

            // 验证每个SKU的价格都被正确处理
            result.getSkuList().forEach(sku -> {
                assertThat(sku.getDropShippingPrice()).isNotNull();
                assertThat(sku.getDropShippingPrice()).isGreaterThan(sku.getPrice());
            });

            // 验证完整的调用链
            verify(processorChain).processProduct(any(ProductProcessingContext.class));
            verify(strategyFactory).getBestPricingStrategy(any(ProductProcessingContext.class));
            verify(defaultPricingStrategy).process(eq(complexProduct), any(ProductProcessingContext.class));
        }

        @Test
        @DisplayName("测试多策略组合应用")
        void testMultipleStrategyComposition() {
            // Given
            UserPricingContext vipUserContext = UserPricingContext.builder()
                .userId(TEST_USER_ID)
                .userLevel(UserPricingContext.UserLevel.PLATINUM)
                .isVip(true)
                .vipLevel(5)
                .markupRate(new BigDecimal("0.10"))
                .discountRate(new BigDecimal("0.95"))
                .build();

            TenantContext enterpriseContext = TenantContext.builder()
                .tenantId(TEST_TENANT_ID)
                .tenantType(TenantContext.TenantType.ENTERPRISE)
                .tenantLevel(TenantContext.TenantLevel.PREMIUM)
                .defaultMarkupRate(new BigDecimal("0.08"))
                .build();

            ProductProcessingContext complexContext = ProductProcessingContext.builder()
                .userId(TEST_USER_ID)
                .tenantId(TEST_TENANT_ID)
                .userPricingContext(vipUserContext)
                .tenantContext(enterpriseContext)
                .productId(testProduct.getId())
                .build();

            // When
            TzProductDTO result = productStrategyService.applyPricingStrategy(testProduct, vipUserContext, enterpriseContext);

            // Then
            assertThat(result).isNotNull();
            verify(strategyFactory).getBestPricingStrategy(any(ProductProcessingContext.class));
        }

        @Test
        @DisplayName("测试系统状态和统计信息")
        void testSystemStatusAndStatistics() {
            // Given
            when(productStrategyService.getSystemStatus()).thenReturn("系统运行正常");
            when(productStrategyService.getExecutionStatistics()).thenReturn("执行统计信息");

            // When
            String systemStatus = productStrategyService.getSystemStatus();
            String executionStats = productStrategyService.getExecutionStatistics();

            // Then
            assertThat(systemStatus).isNotNull();
            assertThat(executionStats).isNotNull();
        }

        private TzProductDTO createComplexTestProduct() {
            TzProductDTO product = new TzProductDTO();
            product.setId(999L);
            product.setTitle("复杂测试商品");
            product.setPdcPlatformProductId("COMPLEX_999");

            // 创建多个不同价格的SKU
            TzProductSkuDTO sku1 = new TzProductSkuDTO();
            sku1.setId(1L);
            sku1.setSku("COMPLEX_SKU_001");
            sku1.setPrice(new BigDecimal("50.00"));
            sku1.setDropShippingPrice(new BigDecimal("50.00"));

            TzProductSkuDTO sku2 = new TzProductSkuDTO();
            sku2.setId(2L);
            sku2.setSku("COMPLEX_SKU_002");
            sku2.setPrice(new BigDecimal("100.00"));
            sku2.setDropShippingPrice(new BigDecimal("100.00"));

            TzProductSkuDTO sku3 = new TzProductSkuDTO();
            sku3.setId(3L);
            sku3.setSku("COMPLEX_SKU_003");
            sku3.setPrice(new BigDecimal("200.00"));
            sku3.setDropShippingPrice(new BigDecimal("200.00"));

            product.setSkuList(Arrays.asList(sku1, sku2, sku3));
            return product;
        }
    }

    // ==================== 性能测试 ====================

    @Nested
    @DisplayName("性能测试")
    class PerformanceTest {

        @Test
        @DisplayName("测试单个产品处理性能")
        void testSingleProductProcessingPerformance() {
            // Given
            int iterations = 1000;

            // When
            long startTime = System.currentTimeMillis();
            for (int i = 0; i < iterations; i++) {
                productStrategyService.processProduct(testProduct, testProcessingContext);
            }
            long endTime = System.currentTimeMillis();

            // Then
            long totalTime = endTime - startTime;
            double averageTime = (double) totalTime / iterations;

            assertThat(totalTime).isLessThan(10000); // 总时间应少于10秒
            assertThat(averageTime).isLessThan(10.0); // 平均每次处理应少于10毫秒

            System.out.printf("性能测试结果: 总时间=%dms, 平均时间=%.2fms%n", totalTime, averageTime);
        }

        @Test
        @DisplayName("测试内存使用情况")
        void testMemoryUsage() {
            // Given
            Runtime runtime = Runtime.getRuntime();
            long initialMemory = runtime.totalMemory() - runtime.freeMemory();

            // When - 处理大量产品
            for (int i = 0; i < 1000; i++) {
                TzProductDTO product = createTestProduct("MEMORY_TEST_" + i, "内存测试商品", new BigDecimal("100.00"));
                productStrategyService.processProduct(product, testProcessingContext);
            }

            // 强制垃圾回收
            System.gc();
            Thread.yield();

            long finalMemory = runtime.totalMemory() - runtime.freeMemory();
            long memoryUsed = finalMemory - initialMemory;

            // Then
            assertThat(memoryUsed).isLessThan(100 * 1024 * 1024); // 内存使用应少于100MB

            System.out.printf("内存使用测试结果: 使用内存=%d bytes (%.2f MB)%n",
                memoryUsed, memoryUsed / (1024.0 * 1024.0));
        }
    }
}
