# 测试环境配置
spring:
    application:
        name: fulfillmen-shop-unit
    profiles:
        active:
            - test
fulfillmen:
  wms:
    base-url: http://192.168.33.18:8888
# 1688 API配置
alibaba:
    api:
        appKey: ${ALIBABA_APP_KEY:8390330}
        appSecret: ${ALIBABA_APP_SECRET:3h27HVrZKU}
        serverUrl: https://gw.open.1688.com/openapi/
        accessToken: ${ALIBABA_ACCESS_TOKEN:b49dfbf3-c3d7-4dfa-9b50-2d3300bad6c2}

fulfillmen-starter:
    captcha:
        graphic:
            expirationInMinutes: 10

# 日志配置
logging:
    level:
        com.fufillmen.shop: DEBUG
        com.fulfillmen.support.alibaba: DEBUG
### MyBatis Plus 配置
mybatis-plus:
  # Mapper XML 文件目录配置
  mapper-locations: classpath*:/mapper/**/*Mapper.xml
  # 类型别名扫描包配置
  type-aliases-package: ${project.base-package}.**.model
  ## MyBatis 配置
  configuration:
    # MyBatis 自动映射策略
    # NONE：不启用 PARTIAL：只对非嵌套 resultMap 自动映射 FULL：对所有 resultMap 自动映射
    auto-mapping-behavior: PARTIAL
  ## 全局配置
  global-config:
    banner: false
    db-config:
      # 主键类型（默认 assign_id，表示自行赋值）
      # auto 代表使用数据库自增策略（需要在表中设置好自增约束）
      id-type: ASSIGN_ID
      # 逻辑删除字段
      logic-delete-field: isDeleted
      # 逻辑删除全局值（默认 id，表示已删除）
      logic-delete-value: id
      # 逻辑未删除全局值（默认 0，表示未删除）
      logic-not-delete-value: 0
  ## 扩展配置
  extension:
    enabled: true
    # Mapper 接口扫描包配置
    mapper-package: ${project.base-package}.**.mapper
    # 开启乐观锁
    optimisticLockerEnabled: true
    # ID 生成器配置
    id-generator:
      type: COSID
    # 分页插件配置
    pagination:
      enabled: true
      db-type: MYSQL
