/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import com.fulfillmen.shop.domain.entity.enums.GenderEnum;
import com.fulfillmen.shop.domain.entity.enums.UserStatusEnum;
import com.fulfillmen.shop.domain.entity.enums.UserTypeEnum;
import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 用户信息;
 *
 * <AUTHOR>
 * @date 2025/4/22 09:57
 * @description: todo
 * @since 1.0.0
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "tz_user")
public class TzUser implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 用户名
     */
    private String username;

    /**
     * 密码加密
     */
    private String password;

    /**
     * 密码盐值
     */
    private String passwordSalt;

    /**
     * 密码版本
     */
    private Integer passwordVersion;

    /**
     * 账号状态 0 unverified-未验证 等待邮箱验证 1 normal-正常 2 locked-锁定 3 disabled-禁用
     */
    private UserStatusEnum status;

    /**
     * 账户余额
     */
    private BigDecimal balance;

    /**
     * 冻结余额
     */
    private BigDecimal frozenBalance;

    /**
     * 可用余额
     */
    private BigDecimal availableBalance;

    /**
     * 注册时间
     */
    private LocalDateTime registerTime;

    /**
     * 注册IP
     */
    private String registerIp;

    /**
     * 最后登录时间
     */
    private LocalDateTime lastLoginTime;

    /**
     * 最后登录IP
     */
    private String lastLoginIp;

    /**
     * 性别 0 未知 1 男 2 女
     */
    private GenderEnum gender;

    /**
     * 出生日期
     */
    private LocalDate birth;

    /**
     * 手机
     */
    private String mobile;

    /**
     * 头像
     */
    private String avatar;

    /**
     * 用户注册类型 0：自注册 1：wms注册
     */
    private UserTypeEnum type;

    /**
     * wms系统客户id
     */
    private Integer wmsCusId;

    /**
     * wms系统客户码
     */
    private String wmsCusCode;

    /**
     * wmsAPi密钥
     */
    @TableField(value = "wms_api_key")
    private String wmsApiKey;

    /**
     * wms系统服务费
     * <pre>
     * 8.000 = 8%
     * </pre>
     */
    private BigDecimal wmsServiceFee;

    /**
     * 国家
     */
    private String country;

    /**
     * 省份
     */
    private String province;

    /**
     * 城市
     */
    private String city;

    /**
     * 地址
     */
    private String address;

    /**
     * 默认仓库
     */
    private Long warehouseId;

    /**
     * 备注
     */
    private String remark;

    /**
     * 租户 id
     */
    private Long tenantId;

    /**
     * 是否删除；0 否，>0 是，默认 0 如果删除，默认将 ID 值赋给 is_deleted
     */
    private Long isDeleted;

    /**
     * 数据版本
     */
    @Version
    private Integer revision;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreated;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;
}
