/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.domain.convert.product;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.fulfillmen.shop.domain.dto.product.alibaba.AlibabaProductAttributeCPVDTO;
import com.fulfillmen.shop.domain.dto.product.alibaba.AlibabaProductCertificateListDTO;
import com.fulfillmen.shop.domain.dto.product.alibaba.AlibabaProductDetailDTO;
import com.fulfillmen.shop.domain.dto.product.alibaba.AlibabaProductSaleInfoDTO;
import com.fulfillmen.shop.domain.dto.product.alibaba.AlibabaProductSellerDataDTO;
import com.fulfillmen.shop.domain.dto.product.alibaba.AlibabaProductShippingInfoDTO;
import com.fulfillmen.shop.domain.dto.product.alibaba.AlibabaProductSkuDTO;
import com.fulfillmen.shop.domain.dto.product.alibaba.AlibabaProductSkuSpecDTO;
import com.fulfillmen.shop.domain.entity.PdcProductMapping;
import com.fulfillmen.shop.domain.entity.enums.PdcProductMappingSyncStatusEnum;
import com.fulfillmen.shop.domain.entity.enums.PlatformCodeEnum;
import com.fulfillmen.shop.domain.entity.enums.PlatformProductStatusEnum;
import com.fulfillmen.shop.domain.entity.json.PdcProductMetaData;
import com.fulfillmen.shop.domain.entity.json.PdcProductSellerData;
import com.fulfillmen.shop.domain.entity.json.PdcProductShippingData;
import com.fulfillmen.shop.domain.entity.json.PdcProductSkuData;
import com.fulfillmen.shop.domain.util.CurrencyConversionUtils;
import com.fulfillmen.support.alibaba.api.response.goods.GoodsDetailResponse;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import me.ahoo.cosid.IdGenerator;
import me.ahoo.cosid.provider.DefaultIdGeneratorProvider;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.DigestUtils;

/**
 * 商品详情转换器
 *
 * <AUTHOR>
 * @date 2025/1/3
 * @description 专门处理商品详情的转换逻辑，从PdcProductConvertMapping中抽取
 * @since 1.0.0
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface PdcProductDetailConvertMapping {

    Logger log = LoggerFactory.getLogger(PdcProductDetailConvertMapping.class);

    /**
     * 1688 商品详情转换为DO
     *
     * @param productDetail 1688 商品详情
     * @param presetId      预设的ID，如果为null则生成新ID
     * @return PdcProductMapping
     */
    default PdcProductMapping createPdcProductMappingByProductDetail1688(GoodsDetailResponse.ProductDetail productDetail,
        Long presetId) {
        // 如果有预设ID则使用，否则生成新ID
        IdGenerator idGenerator = DefaultIdGeneratorProvider.INSTANCE.getRequired("safe-js");
        long id;
        if (presetId != null) {
            id = presetId;
            log.debug("使用预设ID: {} 对应平台商品ID: {}", presetId, productDetail.getOfferId());
        } else {
            id = idGenerator.generate();
            log.debug("生成新ID: {} 对应平台商品ID: {}", id, productDetail.getOfferId());
        }
        PdcProductMapping.PdcProductMappingBuilder builder = PdcProductMapping.builder()
            .id(id)
            .platformCode(PlatformCodeEnum.PLATFORM_CODE_1688)
            .platformProductId(String.valueOf(productDetail.getOfferId()))
            .platformProductTopCategoryId(productDetail.getTopCategoryId() != null
                ? String.valueOf(productDetail.getTopCategoryId())
                : null)
            .platformProductSecondCategoryId(productDetail.getSecondCategoryId() != null
                ? String.valueOf(productDetail.getSecondCategoryId())
                : null)
            .platformProductThirdCategoryId(productDetail.getThirdCategoryId() != null
                ? String.valueOf(productDetail.getThirdCategoryId())
                : null)
            .platformProductName(productDetail.getSubject())
            .platformProductNameTrans(productDetail.getSubjectTrans())
            .isSynced(PdcProductMappingSyncStatusEnum.SYNCED)
            .platformProductCreateDate(productDetail.getCreateDate());

        // 处理图片信息
        if (productDetail.getProductImage() != null) {
            // 设置白底图
            if (productDetail.getProductImage().getWhiteImage() != null) {
                builder.platformProductWhiteImage(productDetail.getProductImage().getWhiteImage());
            }
            // 设置主图（取第一张图片作为主图）
            if (productDetail.getProductImage().getImages() != null && !productDetail.getProductImage()
                .getImages()
                .isEmpty()) {
                builder.platformProductMainImage(productDetail.getProductImage().getImages().get(0));
            }
        }

        // 处理价格信息 - 按照优先级：一件代发价 > 批发价 > priceRangeList价格
        BigDecimal finalPrice = extractOptimalPrice(productDetail);
        if (finalPrice != null) {
            builder.platformProductPrice(finalPrice);
        }

        // 处理基本商品信息
        if (productDetail.getMinOrderQuantity() != null) {
            builder.platformProductMinQuantity(productDetail.getMinOrderQuantity());
        }

        if (productDetail.getSoldOut() != null) {
            builder.platformProductSoldOut(Integer.valueOf(productDetail.getSoldOut()));
        }

        if (productDetail.getProductCargoNumber() != null) {
            builder.platformProductCargoNumber(productDetail.getProductCargoNumber());
        }

        // 处理商品状态（这里需要根据实际的枚举值进行转换）
        if (productDetail.getStatus() != null) {
            builder.platformProductStatus(PlatformProductStatusEnum.findByDesc(productDetail.getStatus()));
        }

        // 处理卖家信息
        if (productDetail.getSellerOpenId() != null) {
            builder.platformProductSellerOpenId(productDetail.getSellerOpenId());
        }

        PdcProductMapping pdcProductMapping = builder.build();

        // 处理JSON字段 - 将复杂对象序列化存储
        try {
            String metaInfo = buildMetaInfo(productDetail);
            pdcProductMapping.setMetaInfo(metaInfo);
            pdcProductMapping.setMetaInfoHash(DigestUtils.md5DigestAsHex(metaInfo.getBytes()));

            if (CollectionUtil.isNotEmpty(productDetail.getProductSkuInfos())) {
                pdcProductMapping.setSkuInfo(buildSkuInfo(productDetail.getProductSkuInfos()));
            }

            if (productDetail.getSellerDataInfo() != null) {
                pdcProductMapping.setSellerDataInfo(buildSellerDataInfo(productDetail.getSellerDataInfo(), productDetail
                    .getSellerOpenId()));
            }

            if (productDetail.getProductShippingInfo() != null) {
                pdcProductMapping.setShippingInfo(buildShippingInfo(productDetail.getProductShippingInfo()));
            }

            if (productDetail.getCertificateList() != null) {
                pdcProductMapping.setCertificateList(buildCertificateListJson(productDetail.getCertificateList()));
            }

        } catch (Exception e) {
            log.warn("序列化商品详情信息时发生异常: {}", e.getMessage());
        }

        // 重置商品ID用于返回
        productDetail.setOfferId(id);

        return pdcProductMapping;
    }

    /**
     * 提取最优价格
     */
    default BigDecimal extractOptimalPrice(GoodsDetailResponse.ProductDetail productDetail) {
        if (productDetail.getProductSaleInfo() != null) {
            GoodsDetailResponse.ProductSaleInfo saleInfo = productDetail.getProductSaleInfo();

            if (saleInfo.getConsignPrice() != null) {
                try {
                    return BigDecimal.valueOf(Double.parseDouble(saleInfo.getConsignPrice()));
                } catch (NumberFormatException e) {
                    log.warn("consignPrice格式异常: {}", saleInfo.getConsignPrice());
                }
            }

            if (CollectionUtil.isNotEmpty(saleInfo.getPriceRangeList())) {
                String price = saleInfo.getPriceRangeList().get(0).getPrice();
                if (price != null) {
                    try {
                        return BigDecimal.valueOf(Double.parseDouble(price));
                    } catch (NumberFormatException e) {
                        log.warn("priceRangeList price格式异常: {}", price);
                    }
                }
            }

            if (saleInfo.getFenxiaoSaleInfo() != null) {
                String onePrice = saleInfo.getFenxiaoSaleInfo().getOnePiecePrice();
                if (onePrice != null) {
                    try {
                        return BigDecimal.valueOf(Double.parseDouble(onePrice));
                    } catch (NumberFormatException e) {
                        log.warn("onePiecePrice格式异常: {}", onePrice);
                    }
                }

                String offerPrice = saleInfo.getFenxiaoSaleInfo().getOfferPrice();
                if (offerPrice != null) {
                    try {
                        return BigDecimal.valueOf(Double.parseDouble(offerPrice));
                    } catch (NumberFormatException e) {
                        log.warn("offerPrice格式异常: {}", offerPrice);
                    }
                }
            }
        }

        if (CollectionUtil.isNotEmpty(productDetail.getProductSkuInfos())) {
            for (GoodsDetailResponse.ProductSkuInfo skuInfo : productDetail.getProductSkuInfos()) {
                if (skuInfo.getConsignPrice() != null) {
                    try {
                        return BigDecimal.valueOf(Double.parseDouble(skuInfo.getConsignPrice()));
                    } catch (NumberFormatException e) {
                        log.warn("SKU consignPrice格式异常: {}", skuInfo.getConsignPrice());
                    }
                }
                if (skuInfo.getPrice() != null) {
                    try {
                        return BigDecimal.valueOf(Double.parseDouble(skuInfo.getPrice()));
                    } catch (NumberFormatException e) {
                        log.warn("SKU price格式异常: {}", skuInfo.getPrice());
                    }
                }
            }
        }
        return null;
    }

    /**
     * 构建商品元数据信息
     */
    default String buildMetaInfo(GoodsDetailResponse.ProductDetail productDetail) {
        PdcProductMetaData.PdcProductMetaDataBuilder builder = PdcProductMetaData.builder()
            .offerId(productDetail.getOfferId())
            .categoryId(productDetail.getCategoryId())
            .subject(productDetail.getSubject())
            .subjectTrans(productDetail.getSubjectTrans())
            .description(productDetail.getDescription())
            .mainVideo(productDetail.getMainVideo())
            .detailVideo(productDetail.getDetailVideo())
            .topCategoryId(productDetail.getTopCategoryId())
            .secondCategoryId(productDetail.getSecondCategoryId())
            .thirdCategoryId(productDetail.getThirdCategoryId())
            .isJxhy(productDetail.getIsJxhy())
            .tradeScore(productDetail.getTradeScore())
            .createDate(productDetail.getCreateDate())
            .status(productDetail.getStatus())
            .minOrderQuantity(productDetail.getMinOrderQuantity())
            .batchNumber(productDetail.getBatchNumber())
            .soldOut(productDetail.getSoldOut())
            .productCargoNumber(productDetail.getProductCargoNumber())
            .sellingPoint(productDetail.getSellingPoint())
            .offerIdentities(productDetail.getOfferIdentities())
            .isSelect(productDetail.getIsSelect())
            .promotionUrl(productDetail.getPromotionUrl());

        if (productDetail.getProductImage() != null) {
            builder.productImages(productDetail.getProductImage().getImages())
                .whiteImage(productDetail.getProductImage().getWhiteImage());
        }

        if (CollectionUtil.isNotEmpty(productDetail.getCertificateList())) {
            List<PdcProductMetaData.CertificateInfo> certificateInfos = productDetail.getCertificateList()
                .stream()
                .map(cert -> PdcProductMetaData.CertificateInfo.builder()
                    .certificateName(cert.getCertificateName())
                    .certificateCode(cert.getCertificateCode())
                    .certificatePhotoList(cert.getCertificatePhotoList())
                    .build())
                .collect(Collectors.toList());
            builder.certificateList(certificateInfos);
        }

        if (CollectionUtil.isNotEmpty(productDetail.getProductAttribute())) {
            List<PdcProductMetaData.ProductAttribute> productAttributes = productDetail.getProductAttribute()
                .stream()
                .map(attr -> PdcProductMetaData.ProductAttribute.builder()
                    .attributeId(attr.getAttributeId())
                    .attributeName(attr.getAttributeName())
                    .value(attr.getValue())
                    .attributeNameTrans(attr.getAttributeNameTrans())
                    .valueTrans(attr.getValueTrans())
                    .build())
                .collect(Collectors.toList());
            builder.productAttributes(productAttributes);
        }

        if (productDetail.getProductSaleInfo() != null) {
            PdcProductMetaData.ProductSaleInfo.ProductSaleInfoBuilder productSaleInfoBuilder = PdcProductMetaData.ProductSaleInfo.builder()
                .amountOnSale(productDetail.getProductSaleInfo().getAmountOnSale())
                .quoteType(productDetail.getProductSaleInfo().getQuoteType())
                .unit(productDetail.getProductSaleInfo().getUnitInfo().getUnit())
                .unitTrans(productDetail.getProductSaleInfo().getUnitInfo().getTransUnit());

            if (CollectionUtil.isNotEmpty(productDetail.getProductSaleInfo().getPriceRangeList())) {
                List<PdcProductMetaData.PriceRange> priceRanges = productDetail.getProductSaleInfo().getPriceRangeList()
                    .stream()
                    .map(priceRange -> PdcProductMetaData.PriceRange.builder()
                        .startQuantity(priceRange.getStartQuantity())
                        .price(priceRange.getPrice())
                        .promotionPrice(priceRange.getPromotionPrice())
                        .build())
                    .collect(Collectors.toList());
                productSaleInfoBuilder.priceRangeList(priceRanges);
            }
            if (productDetail.getProductSaleInfo().getFenxiaoSaleInfo() != null) {
                productSaleInfoBuilder.fenxiaoSaleInfo(PdcProductMetaData.FenxiaoSaleInfo.builder()
                    .onePieceFreePostage(productDetail.getProductSaleInfo()
                        .getFenxiaoSaleInfo()
                        .getOnePieceFreePostage())
                    .startQuantity(productDetail.getProductSaleInfo().getFenxiaoSaleInfo().getStartQuantity())
                    .onePiecePrice(productDetail.getProductSaleInfo().getFenxiaoSaleInfo().getOnePiecePrice())
                    .offerPrice(productDetail.getProductSaleInfo().getFenxiaoSaleInfo().getOfferPrice())
                    .build());
            }
            builder.productSaleInfo(productSaleInfoBuilder.build());
        }

        return JSONUtil.toJsonStr(builder.build());
    }

    /**
     * 构建SKU信息
     */
    default String buildSkuInfo(List<GoodsDetailResponse.ProductSkuInfo> skuInfos) {
        List<PdcProductSkuData> skuDataList = skuInfos.stream()
            .map(this::convertToSkuData)
            .collect(Collectors.toList());
        return JSONUtil.toJsonStr(skuDataList);
    }

    /**
     * 转换单个SKU信息
     */
    default PdcProductSkuData convertToSkuData(GoodsDetailResponse.ProductSkuInfo skuInfo) {
        PdcProductSkuData.PdcProductSkuDataBuilder builder = PdcProductSkuData.builder()
            .skuId(skuInfo.getSkuId())
            .specId(skuInfo.getSpecId())
            .amountOnSale(skuInfo.getAmountOnSale())
            .price(skuInfo.getPrice())
            .consignPrice(skuInfo.getConsignPrice())
            .promotionPrice(skuInfo.getPromotionPrice())
            .cargoNumber(skuInfo.getCargoNumber());

        if (CollectionUtil.isNotEmpty(skuInfo.getSkuAttributes())) {
            List<PdcProductSkuData.SkuAttribute> attributes = skuInfo.getSkuAttributes()
                .stream()
                .map(attr -> PdcProductSkuData.SkuAttribute.builder()
                    .attributeId(attr.getAttributeId())
                    .attributeName(attr.getAttributeName())
                    .attributeNameTrans(attr.getAttributeNameTrans())
                    .value(attr.getValue())
                    .valueTrans(attr.getValueTrans())
                    .skuImageUrl(attr.getSkuImageUrl())
                    .build())
                .collect(Collectors.toList());
            builder.skuAttributes(attributes);
        }

        if (skuInfo.getFenxiaoPriceInfo() != null) {
            PdcProductSkuData.FenxiaoPriceInfo fenxiaoPriceInfo = PdcProductSkuData.FenxiaoPriceInfo.builder()
                .onePiecePrice(skuInfo.getFenxiaoPriceInfo().getOnePiecePrice())
                .offerPrice(skuInfo.getFenxiaoPriceInfo().getOfferPrice())
                .build();
            builder.fenxiaoPriceInfo(fenxiaoPriceInfo);
        }

        return builder.build();
    }

    /**
     * 构建卖家信息
     */
    default String buildSellerDataInfo(GoodsDetailResponse.SellerDataInfo sellerDataInfo, String sellerOpenId) {
        PdcProductSellerData sellerData = PdcProductSellerData.builder()
            .tradeMedalLevel(sellerDataInfo.getTradeMedalLevel())
            .compositeServiceScore(sellerDataInfo.getCompositeServiceScore())
            .logisticsExperienceScore(sellerDataInfo.getLogisticsExperienceScore())
            .disputeComplaintScore(sellerDataInfo.getDisputeComplaintScore())
            .offerExperienceScore(sellerDataInfo.getOfferExperienceScore())
            .afterSalesExperienceScore(sellerDataInfo.getAfterSalesExperienceScore())
            .consultingExperienceScore(sellerDataInfo.getConsultingExperienceScore())
            .repeatPurchasePercent(sellerDataInfo.getRepeatPurchasePercent())
            .collect30DayWithin48HPercent(sellerDataInfo.getCollect30DayWithin48HPercent())
            .qualityRefundWithin30Day(sellerDataInfo.getQualityRefundWithin30Day())
            .sellerOpenId(sellerOpenId)
            .build();

        return JSONUtil.toJsonStr(sellerData);
    }

    /**
     * 构建物流信息
     */
    default String buildShippingInfo(GoodsDetailResponse.ProductShippingInfo shippingInfo) {
        PdcProductShippingData.PdcProductShippingDataBuilder builder = PdcProductShippingData.builder()
            .sendGoodsAddressText(shippingInfo.getSendGoodsAddressText())
            .weight(shippingInfo.getWeight())
            .width(shippingInfo.getWidth())
            .height(shippingInfo.getHeight())
            .length(shippingInfo.getLength())
            .shippingTimeGuarantee(shippingInfo.getShippingTimeGuarantee())
            .pkgSizeSource(shippingInfo.getPkgSizeSource());

        if (CollectionUtil.isNotEmpty(shippingInfo.getSkuShippingDetails())) {
            List<PdcProductShippingData.SkuShippingDetail> skuShippingDetails = shippingInfo.getSkuShippingDetails()
                .stream()
                .map(detail -> PdcProductShippingData.SkuShippingDetail.builder()
                    .skuId(detail.getSkuId())
                    .width(detail.getWidth())
                    .length(detail.getLength())
                    .height(detail.getHeight())
                    .weight(detail.getWeight())
                    .officialLength(detail.getOfficialLength())
                    .officialWidth(detail.getOfficialWidth())
                    .officialHeight(detail.getOfficialHeight())
                    .officialWeight(detail.getOfficialWeight())
                    .aiWeight(detail.getAiWeight())
                    .aiWeightAccuracy(detail.getAiWeightAccuracy())
                    .pkgSizeSource(detail.getPkgSizeSource())
                    .build())
                .collect(Collectors.toList());
            builder.skuShippingDetails(skuShippingDetails);
        }

        return JSONUtil.toJsonStr(builder.build());
    }

    /**
     * 构建证书信息JSON字符串
     */
    default String buildCertificateListJson(List<GoodsDetailResponse.CertificateList> certificateList) {
        if (CollectionUtil.isEmpty(certificateList)) {
            return null;
        }

        try {
            List<PdcProductMetaData.CertificateInfo> certificateInfos = certificateList
                .stream()
                .filter(Objects::nonNull)
                .map(cert -> PdcProductMetaData.CertificateInfo.builder()
                    .certificateName(cert.getCertificateName())
                    .certificateCode(cert.getCertificateCode())
                    .certificatePhotoList(cert.getCertificatePhotoList() != null ? new ArrayList<>(cert.getCertificatePhotoList()) : null)
                    .build())
                .collect(Collectors.toList());

            return JSONUtil.toJsonStr(certificateInfos);
        } catch (Exception e) {
            log.warn("构建证书信息失败: {}", e.getMessage());
            // 如果结构化转换失败，回退到直接序列化
            return JSONUtil.toJsonStr(certificateList);
        }
    }

    /**
     * PdcProductMapping 转换为 AlibabaProductDetailDTO
     *
     * @param pdcProductMapping 商品映射实体
     * @return AlibabaProductDetailDTO
     */
    default AlibabaProductDetailDTO convertToAlibabaProductDetailDTO(PdcProductMapping pdcProductMapping) {
        if (pdcProductMapping == null) {
            return null;
        }

        PdcProductMetaData metaData = null;
        List<PdcProductSkuData> skuDataList = null;
        AlibabaProductSellerDataDTO sellerData = null;
        AlibabaProductShippingInfoDTO shippingData = null;
        List<AlibabaProductCertificateListDTO> certificateList = null;

        try {
            if (pdcProductMapping.getMetaInfo() != null) {
                metaData = JSONUtil.toBean(pdcProductMapping.getMetaInfo(), PdcProductMetaData.class);
            }
            if (pdcProductMapping.getSkuInfo() != null) {
                skuDataList = JSONUtil.toList(pdcProductMapping.getSkuInfo(), PdcProductSkuData.class);
            }
            if (pdcProductMapping.getSellerDataInfo() != null) {
                sellerData = JSONUtil.toBean(pdcProductMapping.getSellerDataInfo(), AlibabaProductSellerDataDTO.class);
            }
            if (pdcProductMapping.getShippingInfo() != null) {
                shippingData = JSONUtil.toBean(pdcProductMapping
                    .getShippingInfo(), AlibabaProductShippingInfoDTO.class);
            }
            // 添加证书信息转换
            if (pdcProductMapping.getCertificateList() != null) {
                certificateList = JSONUtil.toList(pdcProductMapping.getCertificateList(), AlibabaProductCertificateListDTO.class);
            }
        } catch (Exception e) {
            log.error("解析商品JSON数据失败, productId: {}", pdcProductMapping.getId(), e);
        }

        var builder = AlibabaProductDetailDTO.builder()
            .id(pdcProductMapping.getId())
            .platformCode(pdcProductMapping.getPlatformCode())
            .platformProductId(pdcProductMapping.getPlatformProductId())
            .title(pdcProductMapping.getPlatformProductName())
            .titleTrans(pdcProductMapping.getPlatformProductNameTrans())
            .whiteImage(pdcProductMapping.getPlatformProductWhiteImage())
            .sellerOpenId(pdcProductMapping.getPlatformProductSellerOpenId())
            .sellerName(pdcProductMapping.getPlatformProductSellerOpenIdDecrypt())
            .minOrderQuantity(pdcProductMapping.getPlatformProductMinQuantity())
            .sellerDataInfo(sellerData)
            .shippingInfo(shippingData)
            .certificateList(certificateList);

        AlibabaProductSaleInfoDTO saleInfo = null;
        if (metaData != null) {
            builder.categoryId(metaData.getCategoryId())
                .images(metaData.getProductImages())
                .mainVideo(metaData.getMainVideo())
                .detailVideo(metaData.getDetailVideo())
                .description(metaData.getDescription());

            if (CollectionUtil.isNotEmpty(metaData.getProductAttributes())) {
                List<AlibabaProductAttributeCPVDTO> attributes = metaData.getProductAttributes()
                    .stream()
                    .map(attr -> AlibabaProductAttributeCPVDTO.builder()
                        .attributeId(attr.getAttributeId())
                        .attributeName(attr.getAttributeName())
                        .value(attr.getValue())
                        .attributeNameTrans(attr.getAttributeNameTrans())
                        .valueTrans(attr.getValueTrans())
                        .build())
                    .collect(Collectors.toList());
                builder.productAttributeList(attributes);
            }

            saleInfo = buildSaleInfoFromMapping(pdcProductMapping, metaData);
            builder.productSaleInfo(saleInfo);
        }

        BigDecimal finalPrice = pdcProductMapping.getPlatformProductPriceCosign() != null
            ? pdcProductMapping.getPlatformProductPriceCosign()
            : pdcProductMapping.getPlatformProductPrice();
        builder.price(finalPrice);
        builder.usdPrice(calculateUsdPrice(finalPrice));

        if (CollectionUtil.isNotEmpty(skuDataList)) {
            List<AlibabaProductSkuDTO> skuDTOList = skuDataList.stream()
                .map(this::convertToAlibabaProductSkuDTO)
                .collect(Collectors.toList());
            builder.productSkuList(skuDTOList).isSingleItem(false);
        } else {
            builder.isSingleItem(true);
        }

        if (saleInfo != null && saleInfo.getUnitInfo() != null) {
            builder.unit(saleInfo.getUnitInfo().getUnit()).unitTrans(saleInfo.getUnitInfo().getUnitTrans());
        }
        return builder.build();
    }

    /**
     * 从映射实体构建销售信息
     */
    default AlibabaProductSaleInfoDTO buildSaleInfoFromMapping(PdcProductMapping mapping, PdcProductMetaData metaData) {
        PdcProductMetaData.ProductSaleInfo saleInfo = metaData.getProductSaleInfo();
        AlibabaProductSaleInfoDTO.AlibabaProductSaleInfoDTOBuilder builder = AlibabaProductSaleInfoDTO.builder();

        builder.minOrderQuantity(mapping.getPlatformProductMinQuantity());

        if (saleInfo != null) {
            builder.batchNumber(metaData.getBatchNumber());

            if (CollectionUtil.isNotEmpty(saleInfo.getPriceRangeList())) {
                List<AlibabaProductSaleInfoDTO.PriceRange> priceRanges = new ArrayList<>(saleInfo.getPriceRangeList().size());
                saleInfo.getPriceRangeList().forEach(priceRange -> {
                    try {
                        priceRanges.add(AlibabaProductSaleInfoDTO.PriceRange.builder()
                            .startQuantity(priceRange.getStartQuantity())
                            .price(Objects.isNull(priceRange.getPrice())
                                ? null
                                : BigDecimal.valueOf(Double.parseDouble(priceRange.getPrice())))
                            .promotionPrice(Objects.isNull(priceRange.getPromotionPrice())
                                ? null
                                : BigDecimal.valueOf(Double.parseDouble(priceRange.getPromotionPrice())))
                            .build());
                    } catch (NumberFormatException e) {
                        log.warn("价格区间价格格式异常: {}", priceRange.getPrice());
                    }
                });
                builder.priceRangeList(priceRanges);
            }

            if (saleInfo.getQuoteType() == null) {
                saleInfo.setQuoteType(0);
            } else {
                builder.quoteType(saleInfo.getQuoteType());
            }

            builder.unitInfo(AlibabaProductSaleInfoDTO.UnitInfo.builder()
                .unit(saleInfo.getUnit())
                .unitTrans(saleInfo.getUnitTrans())
                .build());

            if (saleInfo.getFenxiaoSaleInfo() != null) {
                BigDecimal onePiecePrice = saleInfo.getFenxiaoSaleInfo().getOnePiecePrice() != null
                    ? BigDecimal.valueOf(Double.parseDouble(saleInfo.getFenxiaoSaleInfo().getOnePiecePrice()))
                    : null;
                BigDecimal offerPrice = saleInfo.getFenxiaoSaleInfo().getOfferPrice() != null
                    ? BigDecimal.valueOf(Double.parseDouble(saleInfo.getFenxiaoSaleInfo().getOfferPrice()))
                    : null;
                builder.fenxiaoSaleInfo(AlibabaProductSaleInfoDTO.FenxiaoSaleInfo.builder()
                    .onePieceFreePostage(saleInfo.getFenxiaoSaleInfo().getOnePieceFreePostage())
                    .startQuantity(saleInfo.getFenxiaoSaleInfo().getStartQuantity())
                    .onePiecePrice(onePiecePrice)
                    .offerPrice(offerPrice)
                    .build());
            }
            builder.amountOnSale(saleInfo.getAmountOnSale());
        }
        return builder.build();
    }

    /**
     * 转换 PdcProductSkuData 到 AlibabaProductSkuDTO
     */
    default AlibabaProductSkuDTO convertToAlibabaProductSkuDTO(PdcProductSkuData skuData) {
        if (skuData == null) {
            return null;
        }

        AlibabaProductSkuDTO.AlibabaProductSkuDTOBuilder builder = AlibabaProductSkuDTO.builder()
            .skuId(skuData.getSkuId())
            .specId(skuData.getSpecId())
            .amountOnSale(skuData.getAmountOnSale())
            .cargoNumber(skuData.getCargoNumber());

        BigDecimal skuPrice = null;
        BigDecimal skuOfferPrice = null;

        if (skuData.getFenxiaoPriceInfo() != null) {
            if (skuData.getFenxiaoPriceInfo().getOnePiecePrice() != null) {
                try {
                    skuPrice = new BigDecimal(skuData.getFenxiaoPriceInfo().getOnePiecePrice());
                } catch (NumberFormatException e) {
                    log.warn("SKU一件代发价格格式异常: {}", skuData.getFenxiaoPriceInfo().getOnePiecePrice());
                }
            }
            if (skuData.getFenxiaoPriceInfo().getOfferPrice() != null) {
                try {
                    skuOfferPrice = new BigDecimal(skuData.getFenxiaoPriceInfo().getOfferPrice());
                } catch (NumberFormatException e) {
                    log.warn("SKU分销价格格式异常: {}", skuData.getFenxiaoPriceInfo().getOfferPrice());
                }
            }
        }

        if (skuPrice == null) {
            try {
                String price = skuData.getPrice() == null ? skuData.getConsignPrice() : skuData.getPrice();
                skuPrice = new BigDecimal(String.valueOf(price == null ? skuOfferPrice : price));
            } catch (NumberFormatException e) {
                log.warn("SKU价格格式异常: {}", skuData.getPrice());
            }
        }

        builder.price(skuPrice)
            .usdPrice(calculateUsdPrice(skuPrice))
            .offerPrice(skuOfferPrice)
            .usdOfferPrice(calculateUsdPrice(skuOfferPrice));

        if (CollectionUtil.isNotEmpty(skuData.getSkuAttributes())) {
            List<AlibabaProductSkuSpecDTO> specs = skuData.getSkuAttributes()
                .stream()
                .map(attr -> AlibabaProductSkuSpecDTO.builder()
                    .attributeId(String.valueOf(attr.getAttributeId()))
                    .attributeName(attr.getAttributeName())
                    .attributeNameTrans(attr.getAttributeNameTrans())
                    .value(attr.getValue())
                    .valueTrans(attr.getValueTrans())
                    .skuImage(attr.getSkuImageUrl())
                    .build())
                .collect(Collectors.toList());
            builder.specs(specs);
        }

        return builder.build();
    }

    /**
     * 计算USD价格
     */
    default BigDecimal calculateUsdPrice(BigDecimal cnyPrice) {
        return CurrencyConversionUtils.cnyToUsd(cnyPrice);
    }
}
