/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.domain.convert.order;

import cn.hutool.core.util.NumberUtil;
import com.fulfillmen.shop.domain.dto.order.UserPurchaseOrderDTO;
import com.fulfillmen.shop.domain.entity.TzOrderItem;
import com.fulfillmen.shop.domain.entity.TzOrderPurchase;
import com.fulfillmen.shop.domain.entity.enums.OrderSupplierSyncStatusEnums;
import com.fulfillmen.shop.domain.entity.enums.TzOrderPurchaseStatusEnum;
import com.fulfillmen.shop.domain.util.CurrencyConversionUtils;
import com.fulfillmen.shop.domain.vo.UserPurchaseOrderPageVO;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @date 2025/8/16 11:05
 * @description: todo
 * @since 1.0.0
 */
@Mapper
public interface UserPurchaseOrderDTOConvert {

    UserPurchaseOrderDTOConvert INSTANCE = Mappers.getMapper(UserPurchaseOrderDTOConvert.class);

    /**
     * 转换采购订单为DTO
     * <pre>
     * 1. 采购订单基本信息转换
     * 2. 订单商品信息转换
     * 3. 汇率转换处理
     * 4. 状态展示信息处理
     * 5. 权限判断处理
     * </pre>
     *
     * @param purchaseOrder 采购订单
     * @param orderItems    订单商品
     * @return UserPurchaseOrderDTO
     */
    @Mappings({
        @Mapping(target = "id", source = "purchaseOrder.id"),
        @Mapping(target = "purchaseOrderNo", source = "purchaseOrder.purchaseOrderNo"),
        @Mapping(target = "buyerId", source = "purchaseOrder.buyerId"),
        @Mapping(target = "buyerType", source = "purchaseOrder.buyerType"),
        @Mapping(target = "orderStatus", source = "purchaseOrder.orderStatus"),
        @Mapping(target = "orderDate", source = "purchaseOrder.orderDate"),
        @Mapping(target = "paidDate", source = "purchaseOrder.paidDate"),
        @Mapping(target = "paidTransactionNo", source = "purchaseOrder.paidTransactionNo"),
        @Mapping(target = "orderCompletedDate", source = "purchaseOrder.orderCompletedDate"),
        @Mapping(target = "serviceFee", source = "purchaseOrder.serviceFee"),
        @Mapping(target = "exchangeRateSnapshot", source = "purchaseOrder.exchangeRateSnapshot"),
        @Mapping(target = "customerGoodsAmount", source = "purchaseOrder.customerGoodsAmount"),
        @Mapping(target = "customerTotalFreight", source = "purchaseOrder.customerTotalFreight"),
        @Mapping(target = "customerTotalAmount", source = "purchaseOrder.customerTotalAmount"),
        @Mapping(target = "currency", expression = "java(getCurrency())"),
        @Mapping(target = "totalQuantity", source = "purchaseOrder.totalQuantity"),
        @Mapping(target = "productTypeCount", expression = "java(orderItems != null ? orderItems.size() : 0)"),
        @Mapping(target = "createTime", source = "purchaseOrder.gmtCreated"),
        @Mapping(target = "updateTime", source = "purchaseOrder.gmtModified"),
        @Mapping(target = "progressPercentage", expression = "java(calculateProgressPercentage(purchaseOrder))"),
        // 美元金额字段
        @Mapping(target = "customerGoodsAmountUsd", expression = "java(convertToUsd(purchaseOrder.getCustomerGoodsAmount(), purchaseOrder.getExchangeRateSnapshot()))"),
        @Mapping(target = "customerTotalAmountUsd", expression = "java(convertToUsd(purchaseOrder.getCustomerTotalAmount(), purchaseOrder.getExchangeRateSnapshot()))"),
        @Mapping(target = "customerTotalFreightUsd", expression = "java(convertToUsd(purchaseOrder.getCustomerTotalFreight(), purchaseOrder.getExchangeRateSnapshot()))"),
        @Mapping(target = "serviceFeeUsd", expression = "java(convertToUsd(purchaseOrder.getServiceFee(), purchaseOrder.getExchangeRateSnapshot()))"),
        // 权限字段
        @Mapping(target = "canCancel", expression = "java(canCancel(purchaseOrder.getOrderStatus()))"),
        @Mapping(target = "canPay", expression = "java(canPay(purchaseOrder.getOrderStatus()))"),
        @Mapping(target = "canConfirmReceipt", expression = "java(canConfirmReceipt(purchaseOrder.getOrderStatus()))"),
        @Mapping(target = "canApplyRefund", expression = "java(canApplyRefund(purchaseOrder.getOrderStatus()))"),
        @Mapping(target = "canViewTracking", expression = "java(canViewTracking(purchaseOrder.getOrderStatus()))"),
        // 主要商品信息
        @Mapping(target = "mainProductTitle", expression = "java(getMainProductTitle(orderItems))"),
        @Mapping(target = "mainProductTitleEn", expression = "java(getMainProductTitleEn(orderItems))"),
        @Mapping(target = "mainProductImageUrl", expression = "java(getMainProductImageUrl(orderItems))"),
        // 统计字段
        @Mapping(target = "supplierCount", source = "purchaseOrder.supplierCount"),
        @Mapping(target = "lineItemCount", source = "purchaseOrder.lineItemCount"),
        @Mapping(target = "completedSupplierCount", source = "purchaseOrder.completedSupplierCount"),
        // 其他字段
        @Mapping(target = "buyerMessage", source = "purchaseOrder.purchaseNotes"),
        @Mapping(target = "deliveryTime", expression = "java(getDeliveryTime(purchaseOrder))"),
        @Mapping(target = "completedTime", expression = "java(getCompletedTime(purchaseOrder))"),
        @Mapping(target = "payTime", source = "purchaseOrder.paidDate"),
        // 订单商品信息
        @Mapping(target = "orderItems", expression = "java(convertOrderItems(orderItems, purchaseOrder.getExchangeRateSnapshot()))"),
        // WMS同步状态字段
        @Mapping(target = "wmsSyncStatus", expression = "java(calculateOverallWmsSyncStatus(purchaseOrder))"),
        @Mapping(target = "wmsFailedMessage", expression = "java(getWmsFailedMessage(purchaseOrder))"),
        @Mapping(target = "canSyncToWms", expression = "java(canSyncToWms(purchaseOrder))")
    })
    UserPurchaseOrderDTO convertToUserPurchaseOrderDTO(TzOrderPurchase purchaseOrder, List<TzOrderItem> orderItems);

    /**
     * 转换DTO为VO
     *
     * @param dtos List<UserPurchaseOrderDTO>
     * @return UserPurchaseOrderPageVO
     */
    List<UserPurchaseOrderPageVO> convertToUserPurchaseOrderPageVO(List<UserPurchaseOrderDTO> dtos);

    /**
     * 转换DTO为VO
     *
     * @param dto UserPurchaseOrderDTO
     * @return UserPurchaseOrderPageVO
     */
    @Mappings({
        @Mapping(target = "orderNo", source = "purchaseOrderNo"),
        @Mapping(target = "payTime", source = "paidDate"),
        @Mapping(target = "productAmount", source = "customerGoodsAmount"),
        @Mapping(target = "productAmountUsd", source = "customerGoodsAmountUsd"),
        @Mapping(target = "totalAmount", source = "customerTotalAmount"),
        @Mapping(target = "totalAmountUsd", source = "customerTotalAmountUsd"),
        @Mapping(target = "shippingFee", source = "customerTotalFreight"),
        @Mapping(target = "shippingFeeUsd", source = "customerTotalFreightUsd")
    })
    UserPurchaseOrderPageVO convertToUserPurchaseOrderPageVO(UserPurchaseOrderDTO dto);

    // ==================== 辅助方法 ====================

    /**
     * 汇率转换为美元
     */
    default BigDecimal convertToUsd(BigDecimal amount, BigDecimal exchangeRateSnapshot) {
        if (amount == null) {
            return null;
        }

        // 如果有汇率快照且不为0，使用快照汇率
        if (NumberUtil.nullToZero(exchangeRateSnapshot).compareTo(BigDecimal.ZERO) > 0) {
            return amount.multiply(exchangeRateSnapshot);
        }

        // 否则使用实时汇率转换
        return CurrencyConversionUtils.cnyToUsd(amount);
    }

    /**
     * 判断订单是否可以取消
     */
    default Boolean canCancel(TzOrderPurchaseStatusEnum status) {
        if (status == null) {
            return false;
        }
        return status.isCancellable();
    }

    /**
     * 判断订单是否可以支付
     */
    default Boolean canPay(TzOrderPurchaseStatusEnum status) {
        if (status == null) {
            return false;
        }
        return status.isPayable();
    }

    /**
     * 判断订单是否可以确认收货 注意：当前状态枚举只到WMS入库，没有发货给客户的状态
     */
    default Boolean canConfirmReceipt(TzOrderPurchaseStatusEnum status) {
        // 当前状态枚举只到WMS入库，没有发货给客户的状态
        // 如果后续需要客户确认收货功能，需要扩展状态枚举
        return false;
    }

    /**
     * 判断订单是否可以申请退款
     */
    default Boolean canApplyRefund(TzOrderPurchaseStatusEnum status) {
        if (status == null) {
            return false;
        }
        // 支付完成后到完成前的状态都可以申请退款
        return status == TzOrderPurchaseStatusEnum.PAYMENT_COMPLETED ||
            status == TzOrderPurchaseStatusEnum.PROCUREMENT_IN_PROGRESS ||
            status == TzOrderPurchaseStatusEnum.SUPPLIER_SHIPPED ||
            status == TzOrderPurchaseStatusEnum.WAREHOUSE_PENDING_RECEIVED ||
            status == TzOrderPurchaseStatusEnum.WAREHOUSE_RECEIVED ||
            status == TzOrderPurchaseStatusEnum.IN_STOCK;
    }

    /**
     * 判断订单是否可以查看物流
     */
    default Boolean canViewTracking(TzOrderPurchaseStatusEnum status) {
        if (status == null) {
            return false;
        }
        // 采购中及以后的状态可以查看物流
        return status == TzOrderPurchaseStatusEnum.PROCUREMENT_IN_PROGRESS ||
            status == TzOrderPurchaseStatusEnum.SUPPLIER_SHIPPED ||
            status == TzOrderPurchaseStatusEnum.WAREHOUSE_PENDING_RECEIVED ||
            status == TzOrderPurchaseStatusEnum.WAREHOUSE_RECEIVED ||
            status == TzOrderPurchaseStatusEnum.IN_STOCK;
    }

    /**
     * 获取主要商品标题
     */
    default String getMainProductTitle(List<TzOrderItem> orderItems) {
        if (orderItems == null || orderItems.isEmpty()) {
            return null;
        }
        return orderItems.get(0).getProductTitle();
    }

    /**
     * 获取主要商品英文标题
     */
    default String getMainProductTitleEn(List<TzOrderItem> orderItems) {
        if (orderItems == null || orderItems.isEmpty()) {
            return null;
        }
        return orderItems.get(0).getProductTitleEn();
    }

    /**
     * 获取主要商品图片
     */
    default String getMainProductImageUrl(List<TzOrderItem> orderItems) {
        if (orderItems == null || orderItems.isEmpty()) {
            return null;
        }
        return orderItems.getFirst().getProductImageUrl();
    }

    /**
     * 获取发货时间
     */
    default LocalDateTime getDeliveryTime(TzOrderPurchase purchaseOrder) {
        // 根据订单状态判断发货时间
        if (purchaseOrder.getOrderStatus() == TzOrderPurchaseStatusEnum.SUPPLIER_SHIPPED ||
            purchaseOrder.getOrderStatus() == TzOrderPurchaseStatusEnum.WAREHOUSE_PENDING_RECEIVED ||
            purchaseOrder.getOrderStatus() == TzOrderPurchaseStatusEnum.WAREHOUSE_RECEIVED ||
            purchaseOrder.getOrderStatus() == TzOrderPurchaseStatusEnum.IN_STOCK) {
            // 这里可以根据实际业务逻辑返回发货时间
            // 暂时返回更新时间作为发货时间的近似值
            return purchaseOrder.getGmtModified();
        }
        return null;
    }

    /**
     * 获取完成时间
     */
    default LocalDateTime getCompletedTime(TzOrderPurchase purchaseOrder) {
        if (purchaseOrder.getOrderStatus() == TzOrderPurchaseStatusEnum.IN_STOCK) {
            return purchaseOrder.getOrderCompletedDate();
        }
        return null;
    }

    /**
     * 转换订单商品信息
     */
    default List<UserPurchaseOrderDTO.OrderItemInfoDTO> convertOrderItems(List<TzOrderItem> orderItems, BigDecimal exchangeRateSnapshot) {
        if (orderItems == null || orderItems.isEmpty()) {
            return List.of();
        }

        return orderItems.stream()
            .map(item -> convertOrderItem(item, exchangeRateSnapshot))
            .toList();
    }

    /**
     * 转换单个订单商品
     */
    default UserPurchaseOrderDTO.OrderItemInfoDTO convertOrderItem(TzOrderItem orderItem, BigDecimal exchangeRateSnapshot) {
        UserPurchaseOrderDTO.OrderItemInfoDTO dto = new UserPurchaseOrderDTO.OrderItemInfoDTO();
        dto.setId(String.valueOf(orderItem.getId()));
        dto.setPurchaseOrderId(orderItem.getPurchaseOrderId());
        dto.setSupplierOrderId(orderItem.getSupplierOrderId());
        dto.setProductId(orderItem.getProductSpuId());
        dto.setSkuId(orderItem.getProductSkuId());
        dto.setProductImage(orderItem.getProductImageUrl());
        dto.setProductTitle(orderItem.getProductTitle());
        dto.setProductTitleEn(orderItem.getProductTitleEn());
        dto.setSkuSpecs(orderItem.getSkuSpecs());
        dto.setOrderedQuantity(orderItem.getQuantity() != null ? orderItem.getQuantity().intValue() : null);
        dto.setUnitOfMeasure(orderItem.getUnit());
        dto.setUnitPrice(orderItem.getPrice());
        dto.setUnitPriceUsd(convertToUsd(orderItem.getPrice(), exchangeRateSnapshot));
        dto.setLineTotalAmount(orderItem.getTotalAmount());
        dto.setLineTotalAmountUsd(convertToUsd(orderItem.getTotalAmount(), exchangeRateSnapshot));
        dto.setItemStatus(orderItem.getStatus());
        // TODO: 需要从供应商订单中获取WMS同步状态
        dto.setSupplierWmsSyncStatus(OrderSupplierSyncStatusEnums.NOT_SYNCED);
        dto.setWmsFailedMessage(null);
        return dto;
    }

    /**
     * 计算订单进度百分比
     */
    default Integer calculateProgressPercentage(TzOrderPurchase purchaseOrder) {
        if (purchaseOrder.getOrderStatus() == null) {
            return 0;
        }

        return switch (purchaseOrder.getOrderStatus()) {
            case TEMPORARILY_SAVED, PAYMENT_PENDING, PAYMENT_PENDING_FOR_PARTIAL -> 0;
            case PAYMENT_COMPLETED -> 15;
            case PENDING_REVIEW -> 20;
            case PROCUREMENT_IN_PROGRESS -> 30;
            case PARTIALLY_PROCUREMENT -> 45;
            case PROCUREMENT_COMPLETED -> 55;
            case SUPPLIER_SHIPPED -> 70;
            case WAREHOUSE_PENDING_RECEIVED -> 85;
            case WAREHOUSE_RECEIVED -> 95;
            case IN_STOCK, ORDER_COMPLETED -> 100;
            case ORDER_CANCELLED, ORDER_REFUNDING, ORDER_REFUNDED -> 0;
        };
    }

    /**
     * 获取币种
     */
    default String getCurrency() {
        // 默认返回人民币
        return "CNY";
    }

    /**
     * 计算整体WMS同步状态
     */
    default OrderSupplierSyncStatusEnums calculateOverallWmsSyncStatus(TzOrderPurchase purchaseOrder) {
        // TODO: 这里需要根据实际业务逻辑计算整体同步状态
        // 目前返回默认值，后续需要在Repository层获取供应商订单数据
        return OrderSupplierSyncStatusEnums.NOT_SYNCED;
    }

    /**
     * 获取WMS同步失败原因
     */
    default String getWmsFailedMessage(TzOrderPurchase purchaseOrder) {
        // TODO: 这里需要根据供应商订单的失败信息聚合
        return null;
    }

    /**
     * 判断是否可以同步到WMS
     */
    default Boolean canSyncToWms(TzOrderPurchase purchaseOrder) {
        if (purchaseOrder.getOrderStatus() == null) {
            return false;
        }
        // 只有支付完成的订单才可以同步到WMS
        return purchaseOrder.getOrderStatus() == TzOrderPurchaseStatusEnum.PAYMENT_COMPLETED ||
            purchaseOrder.getOrderStatus() == TzOrderPurchaseStatusEnum.PROCUREMENT_IN_PROGRESS;
    }
}
