--- ### 项目配置
project:
  # URL（跨域配置默认放行此 URL，第三方登录回调默认使用此 URL 为前缀，请注意更改为你实际的前端 URL）
  url: https://api.nayasource.com
--- ### 配置 回调地址
fulfillmen-shop.alibaba:
  callback-log:
    process-hours: 96


--- ### 数据源配置
spring:
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    url: jdbc:p6spy:mysql://${DB_HOST:dbconn.sealoshzh.site}:${DB_PORT:49664}/${DB_NAME:fulfillmen_shop}?serverTimezone=Asia/Shanghai&useUnicode=true&characterEncoding=utf8&useSSL=false&allowMultiQueries=true&rewriteBatchedStatements=true&autoReconnect=true&maxReconnects=10&failOverReadOnly=false
    username: ${DB_USER:root}
    password: ${DB_PWD:9bzqmhrf}
    driver-class-name: com.p6spy.engine.spy.P6SpyDriver
  # 线程池配置 虚拟线程开启
  threads:
    virtual:
      enabled: true
## Liquibase 配置
  liquibase:
    # 是否启用
    enabled: false
    # 配置文件路径
    change-log: classpath:/db/changelog/db.changelog-master.yaml
  ### 缓存配置
  data:
    ## Redis 配置（单机模式）
    redis:
      # 地址
      host: ${REDIS_HOST:dbconn.sealoshzh.site}
      # 端口（默认 6379）
      port: ${REDIS_PORT:47683}
      # 密码（未设置密码时请注释掉）
      password: ${REDIS_PWD:qnzh5dj2}
      # 数据库索引
      database: ${REDIS_DB:0}
      # 连接超时时间
      timeout: 10s
    ## Redisson 配置
    redisson:
      enabled: true
      mode: SINGLE
## JetCache 配置
jetcache:
  # 统计间隔（默认 0，表示不统计）
  statIntervalMinutes: 15
  hidePackage: com.fulfillmen.shop
  ## 本地/进程级/一级缓存配置
  local:
    default:
      # 缓存类型
      type: caffeine
      # key 转换器的全局配置
      keyConvertor: jackson
      valueConvertor: jackson
      # 以毫秒为单位指定超时时间的全局配置
      expireAfterWriteInMillis: 7200000
      # 每个缓存实例的最大元素的全局配置，仅 local 类型的缓存需要指定
      limit: 1000
  ## 远程/分布式/二级缓存配置
  remote:
    default:
      # 默认 key- 远程前缀
      keyPrefix: "shop:"
      # 缓存类型
      type: redisson
      # key 转换器的全局配置（用于将复杂的 KEY 类型转换为缓存实现可以接受的类型）
      keyConvertor: jackson
      valueConvertor: jackson
      # 以毫秒为单位指定超时时间的全局配置
      expireAfterWriteInMillis: 7200000
      # 2.7+ 支持两级缓存更新以后失效其他 JVM 中的 local cache，但多个服务共用 Redis 同一个 channel 可能会造成广播风暴，需要在这里指定channel。
      # 你可以决定多个不同的服务是否共用同一个 channel，如果没有指定则不开启。
      broadcastChannel: ${spring.application.name}
      # 序列化器的全局配置，仅 remote 类型的缓存需要指定
      valueEncoder: kryo5
      valueDecoder: kryo5

--- ### 日志配置
fulfillmen-starter:
  web:
    ## CORS 跨域配置
    cors:
      enabled: true
      # 配置允许跨域的请求方式
      allowed-methods: '*'
      # 配置允许跨域的请求头
      allowed-headers: '*'
      # 配置允许跨域的响应头
      exposed-headers: '*'
      # 是否允许发送凭证信息（当设置为false时，可以使用通配符域名）
      allow-credentials: true
      # 正则表达式 配置允许跨域的域名
      allowed-origins-regex:
        # 匹配所有 nayasource.com 的子域名（HTTP 和 HTTPS）
        - "http(s)?://(.+\\.)?nayasource\\.com"
        # 匹配所有本地开发端口
        - "http://localhost:[0-9]+"
        # 匹配开发环境域名
        - "http(s)?://(.+\\.)?aliyuncs\\.com"
        - "http(s)?://(.+\\.)?sealoshzh\\.site"
      # 精确域名配置（备用方案）
#      allowed-origins:
#        - "https://api.nayasource.com"
#        - "https://shop.nayasource.com"
#        - "https://admin.nayasource.com"
  log:
    # 是否打印日志，开启后可打印访问日志（类似于 Nginx access log）
    is-print: true
    exclude-patterns:
      - /**.css
      - /**.html
      - /**.js
## 项目日志配置（配置重叠部分，优先级高于 logback-spring.xml 中的配置）
logging:
  level:
    com.fulfillmen.shop: DEBUG
    com.fulfillmen.starter: DEBUG
  file:
    path: ./logs

--- ### 接口文档配置
springdoc:
  # 开发、测试，默认开启
  swagger-ui:
    enabled: true

--- ### WebSocket 配置
#fulfillmen-starter.messaging.websocket:
#    enabled: true
#    path: /websocket
#    # 配置允许跨域的域名
#    allowed-origins: '*'

--- ### Just Auth 配置
justauth:
  enabled: true
  type:
    GITEE:
      client-id: 5d271b7f638941812aaf8bfc2e2f08f06d6235ef934e0e39537e2364eb8452c4
      client-secret: 1f7d08**********5b7**********29e
      redirect-uri: ${project.url}/social/callback?source=gitee
    GITHUB:
      client-id: 38080dad08cfbdfacca9
      client-secret: 1f7d08**********5b7**********29e
      redirect-uri: ${project.url}/social/callback?source=github
  cache:
    type: REDIS
--- ### 安全配置
#fulfillmen-starter.security:
#    ## 字段加/解密配置
#    crypto:
#        enabled: true
#        # 对称加密算法密钥
#        password: abcdefghijklmnop
#        # 非对称加密算法密钥（在线生成 RSA 密钥对：http://web.chacuo.net/netrsakeypair）
#        public-key: MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAM51dgYtMyF+tTQt80sfFOpSV27a7t9uaUVeFrdGiVxscuizE7H8SMntYqfn9lp8a5GH5P1/GGehVjUD2gF/4kcCAwEAAQ==
#        private-key: MIIBVQIBADANBgkqhkiG9w0BAQEFAASCAT8wggE7AgEAAkEAznV2Bi0zIX61NC3zSx8U6lJXbtru325pRV4Wt0aJXGxy6LMTsfxIye1ip+f2WnxrkYfk/X8YZ6FWNQPaAX/iRwIDAQABAkEAk/VcAusrpIqA5Ac2P5Tj0VX3cOuXmyouaVcXonr7f+6y2YTjLQuAnkcfKKocQI/juIRQBFQIqqW/m1nmz1wGeQIhAO8XaA/KxzOIgU0l/4lm0A2Wne6RokJ9HLs1YpOzIUmVAiEA3Q9DQrpAlIuiT1yWAGSxA9RxcjUM/1kdVLTkv0avXWsCIE0X8woEjK7lOSwzMG6RpEx9YHdopjViOj1zPVH61KTxAiBmv/dlhqkJ4rV46fIXELZur0pj6WC3N7a4brR8a+CLLQIhAMQyerWl2cPNVtE/8tkziHKbwW3ZUiBXU24wFxedT9iV
#    ## 密码编码器配置
#    password:
#        enabled: true
#        # BCryptPasswordEncoder
#        encoding-id: bcrypt
#    ## 限流器配置
#    limiter:
#        enabled: true
#        key-prefix: RateLimiter


## 头像支持格式配置
avatar:
  support-suffix: jpg,jpeg,png,gif


## 接口文档增强配置
knife4j:
  enable: true
  # 开启密码验证
  basic:
    enable: true
    username: fulfillmen
    password: Jv3+tB(vs8Yx8e
---
## 其他验证码配置
captcha:
  expirationInMinutes: 2
  ## 邮箱验证码配置
  mail:
    # 内容长度
    length: 6
    # 过期时间
    expirationInMinutes: 5
    # 模板路径
    templatePath: mail/captcha.ftl
    activation-template-path: mail/activation.ftl
    activation-base-url: https://shop.nayasource.com
    activation-expirationInHours: 24
  ## 短信验证码配置
  sms:
    # 内容长度
    length: 6
    # 过期时间
    expirationInMinutes: 5
    # 模板 ID
    templateId: 1
fulfillmen:
  wms:
    base-url: http://wms.fulfillmen.com
