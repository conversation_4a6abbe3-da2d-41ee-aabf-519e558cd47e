/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.secheduler;

import cn.hutool.core.util.NumberUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.fulfillmen.shop.common.tenant.ShopTenantContext;
import com.fulfillmen.shop.dao.mapper.SysAlibabaCallbackLogsMapper;
import com.fulfillmen.shop.domain.entity.SysAlibabaCallbackLogs;
import com.fulfillmen.shop.manager.core.repository.SysAlibabaCallbackLogsRepository;
import com.fulfillmen.shop.secheduler.properties.AlibabaCallbackProperties;
import com.fulfillmen.shop.secheduler.properties.AlibabaCallbackProperties.CallbackLogProperties;
import com.fulfillmen.shop.secheduler.service.AlibabaCallbackProcessingService;
import com.fulfillmen.shop.secheduler.service.AlibabaCallbackProcessingService.CallbackProcessingResult;
import com.fulfillmen.starter.cache.redisson.util.RedisUtils;
import com.fulfillmen.starter.data.mp.tenant.DefaultTenantContext;
import com.yomahub.tlog.id.snowflake.UniqueIdGenerator;
import java.time.LocalDateTime;
import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 系统阿里巴巴回调日志定时任务
 *
 * <pre>
 * 功能1 - 清理历史日志：
 * - 支持通过配置清理历史日志：shop.alibaba.callback-log.retention-days（默认 30 天）
 * - 以 gmt_created 为基准时间清理早于阈值的数据
 * - 分布式锁防并发：仅一个实例执行
 * - 分批删除：每批 1000 条，直到不足一批
 *
 * 功能2 - 处理未处理webhook日志：
 * - 每30秒执行一次：处理6小时内未处理的webhook日志
 * - 查询范围：固定为6小时内创建的未处理记录
 * - 支持配置处理数量：shop.alibaba.callback-log.process-limit（默认 50 条）
 * - 按订单ID分组，取最新的业务时间戳记录（基于 gmt_born_virtual 降序）
 * - 分布式锁防并发：仅一个实例执行
 * </pre>
 *
 * <AUTHOR>
 * @date 2025/8/8
 * @since 1.0.0
 */
@Slf4j
@Profile({"!dev", "!local"})
@Component
@RequiredArgsConstructor
public class AlibabaCallbackLogsScheduledTask {

    private static final String CLEANUP_LOCK_KEY = "scheduled:alibaba:callback-log:cleanup";
    private static final String PROCESS_LOCK_KEY = "scheduled:alibaba:callback-log:process";
    private static final int DEFAULT_RETENTION_DAYS = 30;
    private static final int BATCH_SIZE = 1000;
    // 默认租户 ID
    private static final Long DEFAULT_TENANT_ID = 10000L;

    private final SysAlibabaCallbackLogsMapper callbackLogsMapper;
    private final SysAlibabaCallbackLogsRepository callbackLogsRepository;
    private final AlibabaCallbackProcessingService callbackProcessingService;

    private final AlibabaCallbackProperties alibabaCallbackProperties;

    /**
     * 每日 0 点执行清理
     */
    @Scheduled(cron = "0 0 0 * * ?")
    public void scheduledCleanupOldLogs() {
        CallbackLogProperties callbackLogProperties = alibabaCallbackProperties.getCallbackLog();
        int retentionDays = NumberUtil.max(DEFAULT_RETENTION_DAYS, callbackLogProperties.getRetentionDays());

        // 分布式锁，避免多实例并发执行
        boolean locked = RedisUtils.tryLock(CLEANUP_LOCK_KEY, 600_000L, 60_000L);
        if (!locked) {
            log.info("阿里巴巴回调日志清理任务跳过：未获取到分布式锁");
            return;
        }

        long start = System.currentTimeMillis();
        LocalDateTime cutoff = LocalDateTime.now().minusDays(retentionDays);
        int totalDeleted = 0;
        int batchCount = 0;

        try {
            while (true) {
                // 先查一批 ID，避免大事务与过多内存占用
                LambdaQueryWrapper<SysAlibabaCallbackLogs> selectWrapper = Wrappers
                  .<SysAlibabaCallbackLogs>lambdaQuery()
                  .select(SysAlibabaCallbackLogs::getId, SysAlibabaCallbackLogs::getGmtCreated)
                  .le(SysAlibabaCallbackLogs::getGmtCreated, cutoff)
                  .orderByAsc(SysAlibabaCallbackLogs::getGmtCreated)
                  .last("LIMIT " + BATCH_SIZE);

                List<SysAlibabaCallbackLogs> candidates = callbackLogsMapper.selectList(selectWrapper);
                if (candidates == null || candidates.isEmpty()) {
                    break;
                }

                List<Long> ids = candidates.stream().map(SysAlibabaCallbackLogs::getId).collect(Collectors.toList());

                int deleted = callbackLogsMapper.delete(
                  Wrappers.<SysAlibabaCallbackLogs>lambdaQuery().in(SysAlibabaCallbackLogs::getId, ids));
                totalDeleted += deleted;
                batchCount++;

                if (deleted < BATCH_SIZE) {
                    // 当前批次小于批大小，说明已清理完毕
                    break;
                }
            }

            log.info("阿里巴巴回调日志清理完成：阈值时间={}, 删除条数={}, 批次数={}, 耗时={}ms",
              cutoff, totalDeleted, batchCount, System.currentTimeMillis() - start);
        } catch (Exception e) {
            log.error("阿里巴巴回调日志清理失败：阈值时间={}", cutoff, e);
        } finally {
            RedisUtils.unlock(CLEANUP_LOCK_KEY);
        }
    }

    /**
     * 每30秒执行未处理webhook日志处理 - 混合锁优化版本
     *
     * <pre>
     * 关键改进：
     * 1. 获取所有未处理记录而非仅最新的，避免中间状态丢失
     * 2. 按订单ID分组，每个订单按时间顺序处理所有回调记录
     * 3. 混合锁策略：全局锁保护数据查询，订单锁支持并发处理
     * 4. SKIPPED状态单独标记，不影响其他记录处理
     * 5. 适用于集群部署，避免数据竞态条件
     * </pre>
     */
    @Scheduled(fixedDelay = 30000) // 30秒执行一次
    public void scheduledProcessUnprocessedLogs() {
        CallbackLogProperties callbackLogProperties = alibabaCallbackProperties.getCallbackLog();
        long start = System.currentTimeMillis();
        int totalProcessedCount = 0;
        int processedOrderCount = 0;

        // 第一阶段：使用全局锁保护数据查询
        boolean globalLocked = RedisUtils.tryLock(PROCESS_LOCK_KEY, 10_000L, 5_000L);
        if (!globalLocked) {
            log.info("阿里巴巴回调日志处理任务跳过：未获取到全局分布式锁");
            return;
        }

        Map<String, List<SysAlibabaCallbackLogs>> orderGroupedLogs;

        try {
            // 设置租户上下文
            setupTenantContextForCallback();

            // 在全局锁保护下查询数据，防止多实例查询同一批数据
            List<SysAlibabaCallbackLogs> allUnprocessedLogs = callbackLogsRepository
              .findAllUnprocessedLogsByOrderId(callbackLogProperties.getProcessLimit(), callbackLogProperties.getProcessHours());

            if (allUnprocessedLogs.isEmpty()) {
                log.debug("阿里巴巴回调日志处理任务：没有找到需要处理的记录");
                return;
            }

            // 按订单ID分组，每组按时间排序
            orderGroupedLogs = allUnprocessedLogs.stream()
              .collect(Collectors.groupingBy(
                SysAlibabaCallbackLogs::getOrderId,
                // 保持顺序
                LinkedHashMap::new,
                Collectors.toList()
              ));

            log.info("阿里巴巴回调日志处理任务开始：找到 {} 个订单的 {} 条 {} 小时内未处理记录",
              orderGroupedLogs.size(), allUnprocessedLogs.size(), callbackLogProperties.getProcessHours());

        } finally {
            // 第二阶段：立即释放全局锁，开始使用订单级锁进行并发处理
            RedisUtils.unlock(PROCESS_LOCK_KEY);
        }

        try {
            // 第三阶段：使用订单级锁并发处理不同订单
            for (Map.Entry<String, List<SysAlibabaCallbackLogs>> entry : orderGroupedLogs.entrySet()) {
                String bizId = entry.getKey();
                List<SysAlibabaCallbackLogs> orderLogs = entry.getValue();

                // 按时间排序，确保按正确顺序处理
                List<SysAlibabaCallbackLogs> sortedLogs = orderLogs.stream()
                  .sorted(Comparator.comparing(SysAlibabaCallbackLogs::getGmtBornVirtual))
                  .collect(Collectors.toList());

                // 使用订单级分布式锁，支持不同订单的并发处理
                String orderLockKey = "order:callback:process:" + bizId;
                boolean orderLocked = RedisUtils.tryLock(orderLockKey, 30_000L, 20_000L);

                if (!orderLocked) {
                    log.warn("订单回调处理跳过（未获取到订单锁）: orderId={}, 记录数量={}", bizId, sortedLogs.size());
                    continue;
                }

                try {
                    // 按时间顺序处理该订单的所有回调记录
                    int orderProcessedCount = processOrderCallbacksInSequence(bizId, sortedLogs);
                    totalProcessedCount += orderProcessedCount;
                    processedOrderCount++;
                } finally {
                    RedisUtils.unlock(orderLockKey);
                }
            }

            log.info("阿里巴巴回调日志处理任务完成：处理了 {} 个订单的 {} 条记录，耗时 {}ms",
              processedOrderCount, totalProcessedCount, System.currentTimeMillis() - start);

        } catch (Exception e) {
            log.error("阿里巴巴回调日志处理任务异常", e);
        } finally {
            // 清理租户上下文
            clearTenantContext();
        }
    }

    /**
     * 从回调记录中设置租户上下文 通过订单ID查询相关的租户信息并设置到当前线程
     * <p>
     */
    private void setupTenantContextForCallback() {
        try {
            // 设置租户ID到MyBatis Plus的租户上下文中
            // 这将使后续的数据库查询自动添加tenant_id条件
            ShopTenantContext.setTenantId(DEFAULT_TENANT_ID.toString());
            // 增加执行链路 traceId
            MDC.put("traceId", String.valueOf(UniqueIdGenerator.generateId()));
            MDC.put("tenantId", DEFAULT_TENANT_ID.toString());

        } catch (Exception e) {
            log.error("设置租户上下文失败", e);
        }
    }

    /**
     * 清理租户上下文
     */
    private void clearTenantContext() {
        try {
            // 清理MyBatis Plus的租户上下文
            DefaultTenantContext.clear();
            MDC.clear();
            log.debug("已清理租户上下文");
        } catch (Exception e) {
            log.warn("清理租户上下文时发生异常", e);
            MDC.remove("tenantId");
            MDC.remove("traceId");
            MDC.remove("BizId");
        }
    }

    /**
     * 按时间顺序处理单个订单的所有回调记录
     *
     * @param bizId       业务 id
     * @param orderedLogs 按时间排序的回调记录列表
     * @return 成功处理的记录数量
     */
    private int processOrderCallbacksInSequence(String bizId, List<SysAlibabaCallbackLogs> orderedLogs) {
        int successCount = 0;
        int skipCount = 0;
        int failCount = 0;

        MDC.put("bizId", bizId);

        try {
            log.info("开始按序处理订单回调: bizId={}, 记录数量={}", bizId, orderedLogs.size());

            for (SysAlibabaCallbackLogs callbackLog : orderedLogs) {
                try {
                    // 使用公共服务处理回调
                    CallbackProcessingResult result = callbackProcessingService.processCallback(callbackLog, "定时任务");

                    switch (result.getStatus()) {
                        case SUCCESS -> {
                            // 成功处理，单独标记该记录
                            callbackProcessingService.markProcessed(callbackLog.getId());
                            successCount++;
                            log.info("订单回调处理成功: bizId={}, logId={}, gmtBorn={}",
                              bizId, callbackLog.getId(), callbackLog.getGmtBornVirtual());
                        }
                        case SKIPPED -> {
                            // 关键改进：SKIPPED记录也单独标记，不影响其他记录
                            callbackProcessingService.markProcessed(callbackLog.getId());
                            skipCount++;
                            log.info("订单回调跳过: bizId={}, logId={}, 原因={}",
                              bizId, callbackLog.getId(), result.getMessage());
                        }
                        case FAILED -> {
                            callbackProcessingService.markFailed(callbackLog.getId(), result.getMessage());
                            failCount++;
                            log.error("订单回调失败: bizId={}, logId={}, 原因={}",
                              bizId, callbackLog.getId(), result.getMessage());
                            // 失败时继续处理下一个记录，不中断整个订单的处理流程
                        }
                    }
                } catch (Exception e) {
                    failCount++;
                    log.error("处理订单回调异常: bizId={}, logId={}", bizId, callbackLog.getId(), e);
                    callbackProcessingService.markFailed(callbackLog.getId(), "处理异常: " + e.getMessage());
                }
            }

            log.info("订单回调批量处理完成: bizId={}, 成功={}, 跳过={}, 失败={}, 总数={}", bizId, successCount, skipCount, failCount, orderedLogs.size());
            // 返回实际处理的记录数量
            return successCount + skipCount;

        } finally {
            MDC.remove("bizId");
        }
    }

}
